<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_121467afaaef6dcfbf89d5a031558bfa {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_121467afaaef6dcfbf89d5a031558bfa" ></div>
        
</body>
<script>
    
    
            var map_121467afaaef6dcfbf89d5a031558bfa = L.map(
                "map_121467afaaef6dcfbf89d5a031558bfa",
                {
                    center: [40.712934172834295, -74.00598555208614],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 15,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_fd190e08b0c9f184e919d1e4e202e06d = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_fd190e08b0c9f184e919d1e4e202e06d.addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
            var poly_line_48e270769cccb816480c2afe6acc3ab4 = L.polyline(
                [[40.71284488819741, -74.00599793198695], [40.71288969434411, -74.00599379847762], [40.712934363850735, -74.00598760450526], [40.712978842294355, -74.00597935761355], [40.71302307548484, -74.00596906784733]],
                {"bubblingMouseEvents": true, "color": "blue", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "blue", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.8, "smoothFactor": 1.0, "stroke": true, "weight": 3}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_7a8e44d238f2caf58812f1ac4260890e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1fd5c7843998bc0d30a0930b796e5b03 = $(`<div id="html_1fd5c7843998bc0d30a0930b796e5b03" style="width: 100.0%; height: 100.0%;">Drone Flight Path</div>`)[0];
                popup_7a8e44d238f2caf58812f1ac4260890e.setContent(html_1fd5c7843998bc0d30a0930b796e5b03);
            
        

        poly_line_48e270769cccb816480c2afe6acc3ab4.bindPopup(popup_7a8e44d238f2caf58812f1ac4260890e)
        ;

        
    
    
            var marker_2107e8f1f48b6364d13c8a95908c757e = L.marker(
                [40.71302307548484, -74.00596906784733],
                {
}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
            var icon_669b34512a390d246187d248d0b096a0 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "glyphicon",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_87e20a2fb276ed9d51aa504a2998017c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a7b5103a9c73c3d0b0d0dd8aa4ffd714 = $(`<div id="html_a7b5103a9c73c3d0b0d0dd8aa4ffd714" style="width: 100.0%; height: 100.0%;">Current Drone Position Alt: 48.0m</div>`)[0];
                popup_87e20a2fb276ed9d51aa504a2998017c.setContent(html_a7b5103a9c73c3d0b0d0dd8aa4ffd714);
            
        

        marker_2107e8f1f48b6364d13c8a95908c757e.bindPopup(popup_87e20a2fb276ed9d51aa504a2998017c)
        ;

        
    
    
                marker_2107e8f1f48b6364d13c8a95908c757e.setIcon(icon_669b34512a390d246187d248d0b096a0);
            
    
            var poly_line_851293495b1b6441278e2c3ba551db72 = L.polyline(
                [[40.71284488819741, -74.00599793198695]],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.6, "smoothFactor": 1.0, "stroke": true, "weight": 2}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_c72da308a85ff70fe06ae4a9c625fb10 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0ac9f3b2c81dfc794107aff7351a8649 = $(`<div id="html_0ac9f3b2c81dfc794107aff7351a8649" style="width: 100.0%; height: 100.0%;">Person 0 Trail</div>`)[0];
                popup_c72da308a85ff70fe06ae4a9c625fb10.setContent(html_0ac9f3b2c81dfc794107aff7351a8649);
            
        

        poly_line_851293495b1b6441278e2c3ba551db72.bindPopup(popup_c72da308a85ff70fe06ae4a9c625fb10)
        ;

        
    
    
            var circle_marker_0e7745477ac14641ab8b3f715b4d7720 = L.circleMarker(
                [40.71284488819741, -74.00599793198695],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_8f2404a86d856c325ce5b80049281a67 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_402fd5d770885cf3778cf7d693ba5733 = $(`<div id="html_402fd5d770885cf3778cf7d693ba5733" style="width: 100.0%; height: 100.0%;">Person 0 Confidence: 0.80</div>`)[0];
                popup_8f2404a86d856c325ce5b80049281a67.setContent(html_402fd5d770885cf3778cf7d693ba5733);
            
        

        circle_marker_0e7745477ac14641ab8b3f715b4d7720.bindPopup(popup_8f2404a86d856c325ce5b80049281a67)
        ;

        
    
    
            var poly_line_fb76d94f0bec9029286b9c23a98c80c9 = L.polyline(
                [[40.712897803489355, -74.00598407366343]],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.6, "smoothFactor": 1.0, "stroke": true, "weight": 2}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_c9f52f93efee0105e9c92fa792932771 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d0d19d6fc1ab55cf4509628b60c63d35 = $(`<div id="html_d0d19d6fc1ab55cf4509628b60c63d35" style="width: 100.0%; height: 100.0%;">Person 1 Trail</div>`)[0];
                popup_c9f52f93efee0105e9c92fa792932771.setContent(html_d0d19d6fc1ab55cf4509628b60c63d35);
            
        

        poly_line_fb76d94f0bec9029286b9c23a98c80c9.bindPopup(popup_c9f52f93efee0105e9c92fa792932771)
        ;

        
    
    
            var circle_marker_e164b216610c0ddb0cd839c886a53b55 = L.circleMarker(
                [40.712897803489355, -74.00598407366343],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_6a87557e4b3ea4601bacf75b908cb41c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a8f5a54100d475708a8b63662e658b0c = $(`<div id="html_a8f5a54100d475708a8b63662e658b0c" style="width: 100.0%; height: 100.0%;">Person 1 Confidence: 0.80</div>`)[0];
                popup_6a87557e4b3ea4601bacf75b908cb41c.setContent(html_a8f5a54100d475708a8b63662e658b0c);
            
        

        circle_marker_e164b216610c0ddb0cd839c886a53b55.bindPopup(popup_6a87557e4b3ea4601bacf75b908cb41c)
        ;

        
    
    
            var poly_line_bfb85eaef49ea74a7b855432f0cf8949 = L.polyline(
                [[40.71295055950312, -74.00596950276176]],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.6, "smoothFactor": 1.0, "stroke": true, "weight": 2}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_496032d4c4d3f5cb8fe89b3d921c9a44 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_191efca5905f625cd03c644aeac57e76 = $(`<div id="html_191efca5905f625cd03c644aeac57e76" style="width: 100.0%; height: 100.0%;">Person 2 Trail</div>`)[0];
                popup_496032d4c4d3f5cb8fe89b3d921c9a44.setContent(html_191efca5905f625cd03c644aeac57e76);
            
        

        poly_line_bfb85eaef49ea74a7b855432f0cf8949.bindPopup(popup_496032d4c4d3f5cb8fe89b3d921c9a44)
        ;

        
    
    
            var circle_marker_7be5b9b10b401be2733fa3f147e2053b = L.circleMarker(
                [40.71295055950312, -74.00596950276176],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_3755a6b0d1eaf263637cf30777b47e47 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b56fcf41ae2945a2bad5dd706469986d = $(`<div id="html_b56fcf41ae2945a2bad5dd706469986d" style="width: 100.0%; height: 100.0%;">Person 2 Confidence: 0.80</div>`)[0];
                popup_3755a6b0d1eaf263637cf30777b47e47.setContent(html_b56fcf41ae2945a2bad5dd706469986d);
            
        

        circle_marker_7be5b9b10b401be2733fa3f147e2053b.bindPopup(popup_3755a6b0d1eaf263637cf30777b47e47)
        ;

        
    
    
            var poly_line_6ca29e991dfa82f35e6bee98117ab3b8 = L.polyline(
                [[40.713003849178484, -74.00595332971841]],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.6, "smoothFactor": 1.0, "stroke": true, "weight": 2}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_17981d68605cd143409bfe57fb86723b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_efd22b27fd74637bda7bcedb0ae225b4 = $(`<div id="html_efd22b27fd74637bda7bcedb0ae225b4" style="width: 100.0%; height: 100.0%;">Person 3 Trail</div>`)[0];
                popup_17981d68605cd143409bfe57fb86723b.setContent(html_efd22b27fd74637bda7bcedb0ae225b4);
            
        

        poly_line_6ca29e991dfa82f35e6bee98117ab3b8.bindPopup(popup_17981d68605cd143409bfe57fb86723b)
        ;

        
    
    
            var circle_marker_ad4fd23d026a0776c713979f71730d75 = L.circleMarker(
                [40.713003849178484, -74.00595332971841],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_601d3f47119089708d34834e5250a343 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_07ae19a8aeec34196e4b37275853f451 = $(`<div id="html_07ae19a8aeec34196e4b37275853f451" style="width: 100.0%; height: 100.0%;">Person 3 Confidence: 0.80</div>`)[0];
                popup_601d3f47119089708d34834e5250a343.setContent(html_07ae19a8aeec34196e4b37275853f451);
            
        

        circle_marker_ad4fd23d026a0776c713979f71730d75.bindPopup(popup_601d3f47119089708d34834e5250a343)
        ;

        
    
    
            var poly_line_f9a990708c6c5803a5a16f86c2ba3fd8 = L.polyline(
                [[40.71305780363734, -74.00593544792589]],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.6, "smoothFactor": 1.0, "stroke": true, "weight": 2}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_2c7abdfc82db76b94af2a728b65a1147 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_358c728659884d0c5ecf42aa81e1e9c8 = $(`<div id="html_358c728659884d0c5ecf42aa81e1e9c8" style="width: 100.0%; height: 100.0%;">Person 4 Trail</div>`)[0];
                popup_2c7abdfc82db76b94af2a728b65a1147.setContent(html_358c728659884d0c5ecf42aa81e1e9c8);
            
        

        poly_line_f9a990708c6c5803a5a16f86c2ba3fd8.bindPopup(popup_2c7abdfc82db76b94af2a728b65a1147)
        ;

        
    
    
            var circle_marker_9ca24b891c59cc7aac23794c16cd1dfd = L.circleMarker(
                [40.71305780363734, -74.00593544792589],
                {"bubblingMouseEvents": true, "color": "green", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 3}
            ).addTo(map_121467afaaef6dcfbf89d5a031558bfa);
        
    
        var popup_050b0246c02ea66ac1248d4d7e07ea70 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_88831d8d533b559d77b405c65772c0ac = $(`<div id="html_88831d8d533b559d77b405c65772c0ac" style="width: 100.0%; height: 100.0%;">Person 4 Confidence: 0.80</div>`)[0];
                popup_050b0246c02ea66ac1248d4d7e07ea70.setContent(html_88831d8d533b559d77b405c65772c0ac);
            
        

        circle_marker_9ca24b891c59cc7aac23794c16cd1dfd.bindPopup(popup_050b0246c02ea66ac1248d4d7e07ea70)
        ;

        
    
</script>
</html>