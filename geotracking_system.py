#!/usr/bin/env python3
"""
Geotracking System for Drone-based Person Tracking
Integrates GPS coordinates, mapping, and geospatial analytics
"""

import numpy as np
import json
import time
import math
from collections import deque, defaultdict
from datetime import datetime
import cv2

try:
    import folium
    import geopy.distance
    from geopy.distance import geodesic
    MAPPING_AVAILABLE = True
except ImportError:
    MAPPING_AVAILABLE = False
    print("Warning: folium and geopy not installed. Install with: pip install folium geopy")

class GeoTracker:
    """GPS-based tracking and mapping system for drone applications"""
    
    def __init__(self, initial_lat=0.0, initial_lon=0.0, initial_alt=0.0):
        self.current_position = {
            'latitude': initial_lat,
            'longitude': initial_lon,
            'altitude': initial_alt,
            'timestamp': time.time(),
            'accuracy': 0.0,
            'heading': 0.0,
            'speed': 0.0
        }
        
        # Position history
        self.position_history = deque(maxlen=1000)
        self.flight_path = []
        
        # Geofencing
        self.geofences = {}  # name -> polygon coordinates
        self.geofence_alerts = []
        
        # Person tracking with GPS
        self.tracked_people_geo = {}  # track_id -> geo positions
        self.people_heatmap_data = defaultdict(list)
        
        # Statistics
        self.total_distance_flown = 0.0
        self.max_altitude = 0.0
        self.flight_time = 0.0
        self.start_time = time.time()
        
        # Coordinate system conversion
        self.reference_point = None  # For local coordinate conversion
        self.meters_per_degree_lat = 111320.0  # Approximate
        self.meters_per_degree_lon = 111320.0  # Will be adjusted based on latitude
        
    def update_drone_position(self, lat, lon, alt, accuracy=1.0, heading=0.0, speed=0.0):
        """Update current drone GPS position"""
        previous_pos = self.current_position.copy()
        
        self.current_position.update({
            'latitude': lat,
            'longitude': lon,
            'altitude': alt,
            'timestamp': time.time(),
            'accuracy': accuracy,
            'heading': heading,
            'speed': speed
        })
        
        # Add to history
        self.position_history.append(self.current_position.copy())
        self.flight_path.append((lat, lon, alt, time.time()))
        
        # Calculate distance traveled
        if previous_pos['latitude'] != 0 and previous_pos['longitude'] != 0:
            if MAPPING_AVAILABLE:
                distance = geodesic(
                    (previous_pos['latitude'], previous_pos['longitude']),
                    (lat, lon)
                ).meters
            else:
                # Fallback calculation using Haversine formula
                distance = self._calculate_distance_fallback(
                    previous_pos['latitude'], previous_pos['longitude'], lat, lon
                )
            self.total_distance_flown += distance
        
        # Update statistics
        self.max_altitude = max(self.max_altitude, alt)
        self.flight_time = time.time() - self.start_time
        
        # Update coordinate conversion factors
        self.meters_per_degree_lon = 111320.0 * math.cos(math.radians(lat))
        
        # Set reference point if not set
        if self.reference_point is None:
            self.reference_point = (lat, lon)
        
        # Check geofences
        self._check_geofences()

        return self.current_position

    def _calculate_distance_fallback(self, lat1, lon1, lat2, lon2):
        """Calculate distance using Haversine formula when geopy is not available"""
        # Convert latitude and longitude from degrees to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # Radius of earth in meters
        r = 6371000

        return c * r
    
    def add_geofence(self, name, polygon_coords, fence_type="inclusion"):
        """Add a geofence polygon"""
        self.geofences[name] = {
            'coordinates': polygon_coords,  # List of (lat, lon) tuples
            'type': fence_type,  # "inclusion" or "exclusion"
            'created': time.time()
        }
    
    def _check_geofences(self):
        """Check if current position violates any geofences"""
        current_point = (self.current_position['latitude'], self.current_position['longitude'])
        
        for fence_name, fence_data in self.geofences.items():
            is_inside = self._point_in_polygon(current_point, fence_data['coordinates'])
            
            if fence_data['type'] == "exclusion" and is_inside:
                alert = {
                    'type': 'geofence_violation',
                    'fence_name': fence_name,
                    'position': current_point,
                    'timestamp': time.time(),
                    'message': f"Drone entered exclusion zone: {fence_name}"
                }
                self.geofence_alerts.append(alert)
            elif fence_data['type'] == "inclusion" and not is_inside:
                alert = {
                    'type': 'geofence_violation',
                    'fence_name': fence_name,
                    'position': current_point,
                    'timestamp': time.time(),
                    'message': f"Drone left inclusion zone: {fence_name}"
                }
                self.geofence_alerts.append(alert)
    
    def _point_in_polygon(self, point, polygon):
        """Check if point is inside polygon using ray casting algorithm"""
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def convert_pixel_to_gps(self, pixel_x, pixel_y, frame_width, frame_height, 
                           field_of_view_h=60, field_of_view_v=45):
        """Convert pixel coordinates to GPS coordinates"""
        if self.current_position['altitude'] == 0:
            return None, None
        
        # Calculate ground coverage
        altitude = self.current_position['altitude']
        ground_width = 2 * altitude * math.tan(math.radians(field_of_view_h / 2))
        ground_height = 2 * altitude * math.tan(math.radians(field_of_view_v / 2))
        
        # Convert pixel to relative position
        rel_x = (pixel_x - frame_width / 2) / frame_width
        rel_y = (pixel_y - frame_height / 2) / frame_height
        
        # Calculate ground coordinates relative to drone
        ground_x = rel_x * ground_width
        ground_y = rel_y * ground_height
        
        # Convert to GPS coordinates
        drone_lat = self.current_position['latitude']
        drone_lon = self.current_position['longitude']
        
        # Account for drone heading
        heading_rad = math.radians(self.current_position['heading'])
        rotated_x = ground_x * math.cos(heading_rad) - ground_y * math.sin(heading_rad)
        rotated_y = ground_x * math.sin(heading_rad) + ground_y * math.cos(heading_rad)
        
        # Convert meters to degrees
        lat_offset = rotated_y / self.meters_per_degree_lat
        lon_offset = rotated_x / self.meters_per_degree_lon
        
        person_lat = drone_lat + lat_offset
        person_lon = drone_lon + lon_offset
        
        return person_lat, person_lon
    
    def track_person_geo(self, track_id, pixel_x, pixel_y, frame_width, frame_height, confidence=1.0):
        """Track a person's GPS position"""
        person_lat, person_lon = self.convert_pixel_to_gps(pixel_x, pixel_y, frame_width, frame_height)
        
        if person_lat is None or person_lon is None:
            return None
        
        geo_position = {
            'latitude': person_lat,
            'longitude': person_lon,
            'altitude': 0.0,  # Ground level
            'timestamp': time.time(),
            'confidence': confidence,
            'drone_position': self.current_position.copy()
        }
        
        # Add to tracking history
        if track_id not in self.tracked_people_geo:
            self.tracked_people_geo[track_id] = deque(maxlen=100)
        
        self.tracked_people_geo[track_id].append(geo_position)
        
        # Add to heatmap data
        self.people_heatmap_data[track_id].append((person_lat, person_lon, confidence))
        
        return geo_position
    
    def get_flight_statistics(self):
        """Get comprehensive flight statistics"""
        return {
            'current_position': self.current_position,
            'total_distance_flown': self.total_distance_flown,
            'max_altitude': self.max_altitude,
            'flight_time': self.flight_time,
            'average_speed': self.total_distance_flown / self.flight_time if self.flight_time > 0 else 0,
            'positions_recorded': len(self.position_history),
            'people_tracked': len(self.tracked_people_geo),
            'geofence_alerts': len(self.geofence_alerts),
            'current_accuracy': self.current_position['accuracy']
        }
    
    def create_web_map(self, filename="tracking_map.html", include_people=True):
        """Create an interactive web map with flight path and tracked people"""
        if not MAPPING_AVAILABLE:
            print("Mapping libraries not available. Install with: pip install folium geopy")
            return None
        
        if not self.position_history:
            print("No position data available for mapping")
            return None
        
        # Center map on average position
        center_lat = sum(pos['latitude'] for pos in self.position_history) / len(self.position_history)
        center_lon = sum(pos['longitude'] for pos in self.position_history) / len(self.position_history)
        
        # Create map
        m = folium.Map(location=[center_lat, center_lon], zoom_start=15)
        
        # Add flight path
        flight_coords = [(pos['latitude'], pos['longitude']) for pos in self.position_history]
        folium.PolyLine(
            flight_coords,
            color='blue',
            weight=3,
            opacity=0.8,
            popup='Drone Flight Path'
        ).add_to(m)
        
        # Add current drone position
        if self.current_position['latitude'] != 0:
            folium.Marker(
                [self.current_position['latitude'], self.current_position['longitude']],
                popup=f"Current Drone Position\nAlt: {self.current_position['altitude']:.1f}m",
                icon=folium.Icon(color='red', icon='plane')
            ).add_to(m)
        
        # Add tracked people
        if include_people:
            for track_id, positions in self.tracked_people_geo.items():
                if positions:
                    # Add person trail
                    person_coords = [(pos['latitude'], pos['longitude']) for pos in positions]
                    folium.PolyLine(
                        person_coords,
                        color='green',
                        weight=2,
                        opacity=0.6,
                        popup=f'Person {track_id} Trail'
                    ).add_to(m)
                    
                    # Add current person position
                    last_pos = positions[-1]
                    folium.CircleMarker(
                        [last_pos['latitude'], last_pos['longitude']],
                        radius=5,
                        popup=f"Person {track_id}\nConfidence: {last_pos['confidence']:.2f}",
                        color='green',
                        fill=True
                    ).add_to(m)
        
        # Add geofences
        for fence_name, fence_data in self.geofences.items():
            color = 'red' if fence_data['type'] == 'exclusion' else 'blue'
            folium.Polygon(
                fence_data['coordinates'],
                color=color,
                weight=2,
                opacity=0.8,
                fill=True,
                fillOpacity=0.1,
                popup=f"{fence_name} ({fence_data['type']})"
            ).add_to(m)
        
        # Save map
        m.save(filename)
        print(f"Interactive map saved to {filename}")
        return m
    
    def export_geo_data(self, filename="geo_tracking_data.json"):
        """Export all tracking data to JSON"""
        export_data = {
            'flight_statistics': self.get_flight_statistics(),
            'flight_path': self.flight_path,
            'tracked_people': {
                str(track_id): [
                    {
                        'latitude': pos['latitude'],
                        'longitude': pos['longitude'],
                        'timestamp': pos['timestamp'],
                        'confidence': pos['confidence']
                    } for pos in positions
                ] for track_id, positions in self.tracked_people_geo.items()
            },
            'geofences': self.geofences,
            'geofence_alerts': self.geofence_alerts,
            'export_timestamp': time.time()
        }
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        print(f"Geo tracking data exported to {filename}")
        return export_data

# Simulated GPS data for testing
class GPSSimulator:
    """Simulate GPS data for testing purposes"""
    
    def __init__(self, start_lat=40.7128, start_lon=-74.0060, start_alt=50.0):
        self.lat = start_lat
        self.lon = start_lon
        self.alt = start_alt
        self.heading = 0.0
        self.speed = 5.0  # m/s
        self.time_step = 1.0  # seconds
        
    def get_next_position(self):
        """Generate next GPS position for simulation"""
        # Simple circular flight pattern
        self.heading += 2.0  # degrees per second
        if self.heading >= 360:
            self.heading -= 360
        
        # Move forward
        distance = self.speed * self.time_step
        lat_offset = distance * math.cos(math.radians(self.heading)) / 111320.0
        lon_offset = distance * math.sin(math.radians(self.heading)) / (111320.0 * math.cos(math.radians(self.lat)))
        
        self.lat += lat_offset
        self.lon += lon_offset
        
        # Vary altitude slightly
        self.alt += np.random.normal(0, 1)
        self.alt = max(10, min(100, self.alt))
        
        return {
            'latitude': self.lat,
            'longitude': self.lon,
            'altitude': self.alt,
            'heading': self.heading,
            'speed': self.speed,
            'accuracy': np.random.uniform(0.5, 2.0)
        }

def test_geotracking():
    """Test the geotracking system"""
    print("🌍 Testing Geotracking System")
    print("=" * 40)
    
    # Initialize tracker
    tracker = GeoTracker(40.7128, -74.0060, 50.0)
    gps_sim = GPSSimulator()
    
    # Add a geofence
    tracker.add_geofence("test_zone", [
        (40.7120, -74.0070),
        (40.7130, -74.0070),
        (40.7130, -74.0050),
        (40.7120, -74.0050)
    ], "inclusion")
    
    # Simulate flight
    for i in range(50):
        gps_data = gps_sim.get_next_position()
        tracker.update_drone_position(
            gps_data['latitude'],
            gps_data['longitude'],
            gps_data['altitude'],
            gps_data['accuracy'],
            gps_data['heading'],
            gps_data['speed']
        )
        
        # Simulate tracking some people
        if i % 5 == 0:
            tracker.track_person_geo(1, 320, 240, 640, 480, 0.8)
            tracker.track_person_geo(2, 400, 300, 640, 480, 0.7)
    
    # Print statistics
    stats = tracker.get_flight_statistics()
    print("Flight Statistics:")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.2f}")
        else:
            print(f"  {key}: {value}")
    
    # Create map if possible
    if MAPPING_AVAILABLE:
        tracker.create_web_map("test_flight_map.html")
    
    # Export data
    tracker.export_geo_data("test_geo_data.json")

if __name__ == "__main__":
    test_geotracking()
