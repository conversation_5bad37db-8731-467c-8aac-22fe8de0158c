import cv2
import argparse
import time
from ultralytics import YOLO
import numpy as np
import json
from collections import deque
import matplotlib.pyplot as plt
import math
from typing import List, Tuple, Dict

# Import geotracking system
try:
    from geotracking_system import GeoTracker, GPSSimulator
    GEOTRACKING_AVAILABLE = True
except ImportError:
    GEOTRACKING_AVAILABLE = False
    print("Warning: Geotracking system not available. Some features may be limited.")

try:
    from deep_sort_realtime.deepsort_tracker import DeepSort
    DEEPSORT_AVAILABLE = True
except ImportError:
    DEEPSORT_AVAILABLE = False
    print("Warning: deep_sort_realtime not installed. Tracking will be disabled.")

class PerspectiveMapper:
    """Handle perspective transformation and coordinate mapping for bird's-eye view"""

    def __init__(self, frame_width: int, frame_height: int, map_width: int = 800, map_height: int = 600):
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.map_width = map_width
        self.map_height = map_height

        # Default perspective points (can be calibrated)
        self.src_points = None
        self.dst_points = None
        self.perspective_matrix = None
        self.inverse_perspective_matrix = None

        # Calibration state
        self.is_calibrated = False
        self.calibration_points = []
        self.calibration_mode = False

        # Real-world scale (pixels per meter) - can be calibrated
        self.pixels_per_meter = 50  # Default: 50 pixels = 1 meter

    def set_default_perspective_points(self):
        """Set default perspective transformation points for typical overhead view"""
        # Source points: trapezoid in the original frame (ground plane)
        margin_x = self.frame_width * 0.2
        margin_y_top = self.frame_height * 0.4
        margin_y_bottom = self.frame_height * 0.1

        self.src_points = np.float32([
            [margin_x, margin_y_top],                           # Top-left
            [self.frame_width - margin_x, margin_y_top],        # Top-right
            [self.frame_width - margin_y_bottom, self.frame_height - margin_y_bottom],  # Bottom-right
            [margin_y_bottom, self.frame_height - margin_y_bottom]  # Bottom-left
        ])

        # Destination points: rectangle in the bird's-eye view
        margin = 50
        self.dst_points = np.float32([
            [margin, margin],                                   # Top-left
            [self.map_width - margin, margin],                  # Top-right
            [self.map_width - margin, self.map_height - margin], # Bottom-right
            [margin, self.map_height - margin]                  # Bottom-left
        ])

        self._calculate_transformation_matrices()

    def set_custom_perspective_points(self, src_points: np.ndarray, dst_points: np.ndarray = None):
        """Set custom perspective transformation points"""
        self.src_points = np.float32(src_points)

        if dst_points is None:
            # Use default destination rectangle
            margin = 50
            self.dst_points = np.float32([
                [margin, margin],
                [self.map_width - margin, margin],
                [self.map_width - margin, self.map_height - margin],
                [margin, self.map_height - margin]
            ])
        else:
            self.dst_points = np.float32(dst_points)

        self._calculate_transformation_matrices()

    def _calculate_transformation_matrices(self):
        """Calculate perspective transformation matrices"""
        if self.src_points is not None and self.dst_points is not None:
            self.perspective_matrix = cv2.getPerspectiveTransform(self.src_points, self.dst_points)
            self.inverse_perspective_matrix = cv2.getPerspectiveTransform(self.dst_points, self.src_points)
            self.is_calibrated = True

    def transform_point(self, point: Tuple[float, float]) -> Tuple[float, float]:
        """Transform a point from camera view to bird's-eye view"""
        if not self.is_calibrated:
            return point

        # Convert point to homogeneous coordinates
        point_homo = np.array([[point[0], point[1]]], dtype=np.float32)

        # Apply perspective transformation
        transformed = cv2.perspectiveTransform(point_homo.reshape(-1, 1, 2), self.perspective_matrix)

        return (float(transformed[0, 0, 0]), float(transformed[0, 0, 1]))

    def transform_points(self, points: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """Transform multiple points from camera view to bird's-eye view"""
        if not self.is_calibrated or not points:
            return points

        # Convert points to numpy array
        points_array = np.array(points, dtype=np.float32).reshape(-1, 1, 2)

        # Apply perspective transformation
        transformed = cv2.perspectiveTransform(points_array, self.perspective_matrix)

        # Convert back to list of tuples
        return [(float(pt[0, 0]), float(pt[0, 1])) for pt in transformed]

    def inverse_transform_point(self, point: Tuple[float, float]) -> Tuple[float, float]:
        """Transform a point from bird's-eye view back to camera view"""
        if not self.is_calibrated:
            return point

        point_homo = np.array([[point[0], point[1]]], dtype=np.float32)
        transformed = cv2.perspectiveTransform(point_homo.reshape(-1, 1, 2), self.inverse_perspective_matrix)

        return (float(transformed[0, 0, 0]), float(transformed[0, 0, 1]))

    def transform_frame(self, frame: np.ndarray) -> np.ndarray:
        """Transform entire frame to bird's-eye view"""
        if not self.is_calibrated:
            return cv2.resize(frame, (self.map_width, self.map_height))

        return cv2.warpPerspective(frame, self.perspective_matrix, (self.map_width, self.map_height))

    def calculate_real_world_distance(self, point1: Tuple[float, float], point2: Tuple[float, float]) -> float:
        """Calculate real-world distance between two points in meters"""
        if not self.is_calibrated:
            # Fallback to pixel distance
            dx = point2[0] - point1[0]
            dy = point2[1] - point1[1]
            return math.sqrt(dx*dx + dy*dy) / self.pixels_per_meter

        # Transform points to bird's-eye view
        transformed_p1 = self.transform_point(point1)
        transformed_p2 = self.transform_point(point2)

        # Calculate distance in pixels
        dx = transformed_p2[0] - transformed_p1[0]
        dy = transformed_p2[1] - transformed_p1[1]
        pixel_distance = math.sqrt(dx*dx + dy*dy)

        # Convert to meters
        return pixel_distance / self.pixels_per_meter

    def draw_perspective_overlay(self, frame: np.ndarray) -> np.ndarray:
        """Draw perspective transformation overlay on frame"""
        overlay = frame.copy()

        if self.src_points is not None:
            # Draw source quadrilateral
            pts = self.src_points.astype(np.int32)
            cv2.polylines(overlay, [pts], True, (0, 255, 255), 2)

            # Draw corner points
            for i, pt in enumerate(pts):
                cv2.circle(overlay, tuple(pt), 8, (0, 255, 255), -1)
                cv2.putText(overlay, f'{i+1}', (pt[0]-10, pt[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

        return overlay

class RealTimeMap:
    """Create and maintain a real-time bird's-eye view map with person tracking"""

    def __init__(self, width: int = 800, height: int = 600, perspective_mapper: PerspectiveMapper = None):
        self.width = width
        self.height = height
        self.perspective_mapper = perspective_mapper

        # Map visualization
        self.map_image = np.zeros((height, width, 3), dtype=np.uint8)
        self.background_map = None
        self.trail_map = np.zeros((height, width, 3), dtype=np.uint8)

        # Track trails and history
        self.track_trails = {}  # track_id -> list of positions
        self.track_colors = {}  # track_id -> color
        self.max_trail_length = 50

        # Density and analytics
        self.density_map = np.zeros((height, width), dtype=np.float32)
        self.density_decay = 0.95  # How fast density fades

        # Zone definitions (can be customized)
        self.zones = {}  # zone_name -> polygon points
        self.zone_counts = {}  # zone_name -> current count

        # Statistics
        self.total_distance_traveled = {}  # track_id -> total distance
        self.track_speeds = {}  # track_id -> recent speeds

    def set_background_from_frame(self, frame: np.ndarray):
        """Set background map from a transformed frame"""
        if self.perspective_mapper and self.perspective_mapper.is_calibrated:
            self.background_map = self.perspective_mapper.transform_frame(frame)
        else:
            self.background_map = cv2.resize(frame, (self.width, self.height))

    def add_zone(self, zone_name: str, polygon_points: List[Tuple[int, int]]):
        """Add a zone for counting people in specific areas"""
        self.zones[zone_name] = np.array(polygon_points, dtype=np.int32)
        self.zone_counts[zone_name] = 0

    def update_tracks(self, tracks: List[Dict], frame_num: int):
        """Update map with current track positions"""
        try:
            # Reset current map
            if self.background_map is not None:
                self.map_image = self.background_map.copy()
            else:
                self.map_image.fill(0)

            # Update density map (fade previous density)
            self.density_map *= self.density_decay

            # Reset zone counts
            for zone_name in self.zone_counts:
                self.zone_counts[zone_name] = 0

            current_positions = {}

            if not tracks:
                return current_positions

            for track in tracks:
                if not isinstance(track, dict) or 'track_id' not in track or 'bbox' not in track:
                    continue

                track_id = track['track_id']
                bbox = track['bbox']

                # Ensure track_id is an integer for consistent handling
                if isinstance(track_id, str):
                    try:
                        if '_' in str(track_id):
                            track_id = int(str(track_id).split('_')[-1])
                        else:
                            track_id = int(track_id)
                    except (ValueError, AttributeError):
                        track_id = hash(str(track_id)) % 10000

                # Validate bbox format
                if not isinstance(bbox, (list, tuple)) or len(bbox) < 4:
                    continue

                # Calculate person center point
                try:
                    if len(bbox) == 4:
                        center_x = float(bbox[0]) + float(bbox[2]) / 2
                        center_y = float(bbox[1]) + float(bbox[3]) / 2
                    else:
                        center_x = (float(bbox[0]) + float(bbox[2])) / 2
                        center_y = (float(bbox[1]) + float(bbox[3])) / 2
                except (ValueError, TypeError):
                    continue

                # Transform to map coordinates
                try:
                    if self.perspective_mapper and self.perspective_mapper.is_calibrated:
                        map_pos = self.perspective_mapper.transform_point((center_x, center_y))
                    else:
                        # Simple scaling if no perspective transformation
                        map_pos = (
                            center_x * self.width / 1920,  # Assume 1920 width default
                            center_y * self.height / 1080  # Assume 1080 height default
                        )

                    # Ensure position is within map bounds
                    map_x = max(0, min(self.width - 1, int(map_pos[0])))
                    map_y = max(0, min(self.height - 1, int(map_pos[1])))
                    current_positions[track_id] = (map_x, map_y)

                    # Update track trail
                    if track_id not in self.track_trails:
                        self.track_trails[track_id] = deque(maxlen=self.max_trail_length)
                        self.track_colors[track_id] = self._generate_track_color(track_id)
                        self.total_distance_traveled[track_id] = 0.0
                        self.track_speeds[track_id] = deque(maxlen=10)

                    self.track_trails[track_id].append((map_x, map_y, frame_num))

                    # Calculate speed if we have previous position
                    if len(self.track_trails[track_id]) > 1:
                        prev_pos = self.track_trails[track_id][-2]
                        distance = math.sqrt((map_x - prev_pos[0])**2 + (map_y - prev_pos[1])**2)
                        self.total_distance_traveled[track_id] += distance

                        # Calculate speed (pixels per frame, can be converted to real units)
                        frame_diff = frame_num - prev_pos[2]
                        if frame_diff > 0:
                            speed = distance / frame_diff
                            self.track_speeds[track_id].append(speed)

                    # Update density map
                    self._update_density_at_position(map_x, map_y)

                    # Check zone membership
                    for zone_name, zone_polygon in self.zones.items():
                        if cv2.pointPolygonTest(zone_polygon, (map_x, map_y), False) >= 0:
                            self.zone_counts[zone_name] += 1

                except Exception as e:
                    print(f"Warning: Error processing track {track_id}: {e}")
                    continue

            # Draw trails
            self._draw_trails()

            # Draw current positions
            self._draw_current_positions(current_positions)

            # Draw zones
            self._draw_zones()

            return current_positions

        except Exception as e:
            print(f"Warning: Error updating real-time map: {e}")
            return {}

    def _generate_track_color(self, track_id: int) -> Tuple[int, int, int]:
        """Generate a unique color for each track"""
        # Use track_id to generate consistent colors
        np.random.seed(track_id)
        color = tuple(np.random.randint(50, 255, 3).tolist())
        np.random.seed()  # Reset seed
        return color

    def _update_density_at_position(self, x: int, y: int, radius: int = 15):
        """Update density map at given position"""
        # Create a circular mask
        y_coords, x_coords = np.ogrid[:self.height, :self.width]
        mask = (x_coords - x)**2 + (y_coords - y)**2 <= radius**2

        # Add density
        self.density_map[mask] += 0.1

        # Clip to maximum value
        self.density_map = np.clip(self.density_map, 0, 1.0)

    def _draw_trails(self):
        """Draw track trails on the map"""
        for track_id, trail in self.track_trails.items():
            if len(trail) < 2:
                continue

            color = self.track_colors[track_id]

            # Draw trail lines
            for i in range(1, len(trail)):
                start_pos = (trail[i-1][0], trail[i-1][1])
                end_pos = (trail[i][0], trail[i][1])

                # Fade older trail segments
                alpha = i / len(trail)
                faded_color = tuple(int(c * alpha) for c in color)

                cv2.line(self.map_image, start_pos, end_pos, faded_color, 2)

    def _draw_current_positions(self, positions: Dict[int, Tuple[int, int]]):
        """Draw current track positions"""
        for track_id, (x, y) in positions.items():
            color = self.track_colors.get(track_id, (255, 255, 255))

            # Draw person circle
            cv2.circle(self.map_image, (x, y), 8, color, -1)
            cv2.circle(self.map_image, (x, y), 10, (255, 255, 255), 2)

            # Draw track ID
            cv2.putText(self.map_image, str(track_id), (x + 12, y - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    def _draw_zones(self):
        """Draw defined zones on the map"""
        for zone_name, zone_polygon in self.zones.items():
            # Draw zone boundary
            cv2.polylines(self.map_image, [zone_polygon], True, (0, 255, 255), 2)

            # Draw zone label and count
            center = np.mean(zone_polygon, axis=0).astype(int)
            label = f"{zone_name}: {self.zone_counts[zone_name]}"
            cv2.putText(self.map_image, label, tuple(center),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

    def get_density_visualization(self) -> np.ndarray:
        """Get density map as a colored visualization"""
        # Normalize density map
        normalized = (self.density_map * 255).astype(np.uint8)

        # Apply colormap
        colored = cv2.applyColorMap(normalized, cv2.COLORMAP_JET)

        return colored

    def get_statistics(self) -> Dict:
        """Get comprehensive map statistics"""
        stats = {
            'total_tracks': len(self.track_trails),
            'active_tracks': len([t for t in self.track_trails.values() if len(t) > 0]),
            'zone_counts': self.zone_counts.copy(),
            'total_distances': self.total_distance_traveled.copy(),
            'average_speeds': {}
        }

        # Calculate average speeds
        for track_id, speeds in self.track_speeds.items():
            if speeds:
                stats['average_speeds'][track_id] = sum(speeds) / len(speeds)

        return stats

class TrackQualityManager:
    """Enhanced track management for accurate person counting with spatial analysis"""

    def __init__(self, min_track_duration=15, confidence_threshold=0.6,
                 max_track_distance=100, track_merge_threshold=0.8, perspective_mapper=None):
        self.min_track_duration = min_track_duration
        self.confidence_threshold = confidence_threshold
        self.max_track_distance = max_track_distance
        self.track_merge_threshold = track_merge_threshold
        self.perspective_mapper = perspective_mapper

        # Track history and quality metrics
        self.track_histories = {}  # track_id -> list of detections
        self.track_qualities = {}  # track_id -> quality score
        self.confirmed_tracks = set()  # track_ids that are confirmed unique
        self.merged_tracks = {}  # old_track_id -> new_track_id mapping
        self.track_positions = {}  # track_id -> recent positions for movement analysis

        # Spatial analysis
        self.track_real_positions = {}  # track_id -> real-world positions
        self.track_distances = {}  # track_id -> total distance traveled
        self.spatial_clusters = {}  # For detecting groups of people

    def update_track_history(self, track_id, bbox, confidence, frame_num):
        """Update track history with new detection including spatial analysis"""
        if track_id not in self.track_histories:
            self.track_histories[track_id] = []
            self.track_positions[track_id] = deque(maxlen=10)
            self.track_real_positions[track_id] = deque(maxlen=10)
            self.track_distances[track_id] = 0.0

        detection_info = {
            'bbox': bbox,
            'confidence': confidence,
            'frame': frame_num,
            'timestamp': time.time()
        }

        self.track_histories[track_id].append(detection_info)

        # Store position for movement analysis
        center_x = bbox[0] + bbox[2] / 2
        center_y = bbox[1] + bbox[3] / 2
        self.track_positions[track_id].append((center_x, center_y))

        # Transform to real-world coordinates if perspective mapper is available
        if self.perspective_mapper and self.perspective_mapper.is_calibrated:
            real_pos = self.perspective_mapper.transform_point((center_x, center_y))
            self.track_real_positions[track_id].append(real_pos)

            # Calculate real-world distance traveled
            if len(self.track_real_positions[track_id]) > 1:
                prev_pos = self.track_real_positions[track_id][-2]
                curr_pos = self.track_real_positions[track_id][-1]
                distance = self.perspective_mapper.calculate_real_world_distance(prev_pos, curr_pos)
                self.track_distances[track_id] += distance
        else:
            # Fallback to pixel coordinates
            self.track_real_positions[track_id].append((center_x, center_y))

            # Calculate pixel distance
            if len(self.track_positions[track_id]) > 1:
                prev_pos = self.track_positions[track_id][-2]
                curr_pos = self.track_positions[track_id][-1]
                distance = math.sqrt((curr_pos[0] - prev_pos[0])**2 + (curr_pos[1] - prev_pos[1])**2)
                self.track_distances[track_id] += distance

        # Update track quality
        self._update_track_quality(track_id)

    def _update_track_quality(self, track_id):
        """Calculate track quality score based on various factors"""
        if track_id not in self.track_histories:
            return

        history = self.track_histories[track_id]
        if len(history) < 3:
            self.track_qualities[track_id] = 0.0
            return

        # Factors for quality scoring
        duration_score = min(len(history) / self.min_track_duration, 1.0)

        # Confidence consistency
        confidences = [det['confidence'] for det in history[-10:] if det.get('confidence') is not None]  # Last 10 detections
        if confidences:
            avg_confidence = sum(confidences) / len(confidences)
            confidence_score = min(avg_confidence / self.confidence_threshold, 1.0)
        else:
            confidence_score = 0.5  # Default if no valid confidences

        # Movement consistency (not too erratic)
        movement_score = self._calculate_movement_score(track_id)

        # Detection frequency (how often detected in recent frames)
        recent_frames = [det['frame'] for det in history[-20:]]
        if len(recent_frames) > 1:
            frame_gaps = [recent_frames[i] - recent_frames[i-1] for i in range(1, len(recent_frames))]
            avg_gap = sum(frame_gaps) / len(frame_gaps)
            frequency_score = max(0, 1.0 - (avg_gap - 1) / 5.0)  # Penalize large gaps
        else:
            frequency_score = 0.5

        # Combined quality score
        quality = (duration_score * 0.3 + confidence_score * 0.3 +
                  movement_score * 0.2 + frequency_score * 0.2)

        self.track_qualities[track_id] = quality

    def _calculate_movement_score(self, track_id):
        """Calculate movement consistency score"""
        if track_id not in self.track_positions or len(self.track_positions[track_id]) < 3:
            return 0.5

        positions = list(self.track_positions[track_id])

        # Calculate movement distances
        distances = []
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            dy = positions[i][1] - positions[i-1][1]
            distance = np.sqrt(dx*dx + dy*dy)
            distances.append(distance)

        if not distances:
            return 0.5

        # Penalize erratic movement (very high variance in distances)
        avg_distance = sum(distances) / len(distances)
        if avg_distance == 0:
            return 1.0

        variance = sum((d - avg_distance)**2 for d in distances) / len(distances)
        cv = np.sqrt(variance) / avg_distance  # Coefficient of variation

        # Good movement has moderate, consistent motion
        movement_score = max(0, 1.0 - cv / 2.0)  # Penalize high coefficient of variation

        return movement_score

    def should_confirm_track(self, track_id):
        """Determine if a track should be confirmed as a unique person"""
        if track_id in self.confirmed_tracks:
            return True

        if track_id not in self.track_qualities:
            return False

        quality = self.track_qualities[track_id]
        history_length = len(self.track_histories.get(track_id, []))

        # Confirm if quality is high enough and duration is sufficient
        if quality >= 0.7 and history_length >= self.min_track_duration:
            self.confirmed_tracks.add(track_id)
            return True

        return False

    def detect_and_merge_duplicate_tracks(self, current_tracks):
        """Detect and merge tracks that likely represent the same person"""
        for i, track1 in enumerate(current_tracks):
            for track2 in current_tracks[i+1:]:
                track_id1 = track1['track_id']
                track_id2 = track2['track_id']

                if self._should_merge_tracks(track1, track2):
                    # Merge track2 into track1 (keep the one with better quality)
                    quality1 = self.track_qualities.get(track_id1, 0)
                    quality2 = self.track_qualities.get(track_id2, 0)

                    if quality1 >= quality2:
                        self.merged_tracks[track_id2] = track_id1
                        if track_id2 in self.confirmed_tracks:
                            self.confirmed_tracks.discard(track_id2)
                    else:
                        self.merged_tracks[track_id1] = track_id2
                        if track_id1 in self.confirmed_tracks:
                            self.confirmed_tracks.discard(track_id1)

    def _should_merge_tracks(self, track1, track2):
        """Determine if two tracks should be merged"""
        bbox1 = track1['bbox']
        bbox2 = track2['bbox']

        # Calculate IoU
        iou = self._calculate_iou(bbox1, bbox2)

        # Calculate distance between centers
        center1 = (bbox1[0] + bbox1[2]/2, bbox1[1] + bbox1[3]/2)
        center2 = (bbox2[0] + bbox2[2]/2, bbox2[1] + bbox2[3]/2)
        distance = np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)

        # Merge if high overlap or very close
        return iou > self.track_merge_threshold or distance < 50

    def _calculate_iou(self, bbox1, bbox2):
        """Calculate Intersection over Union of two bounding boxes"""
        x1_1, y1_1, w1, h1 = bbox1
        x2_1, y2_1 = x1_1 + w1, y1_1 + h1

        x1_2, y1_2, w2, h2 = bbox2
        x2_2, y2_2 = x1_2 + w2, y1_2 + h2

        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        union = w1 * h1 + w2 * h2 - intersection

        return intersection / union if union > 0 else 0.0

    def get_confirmed_unique_count(self):
        """Get count of confirmed unique persons"""
        return len(self.confirmed_tracks)

    def cleanup_old_tracks(self, current_frame, max_age_frames=100):
        """Remove old track data to prevent memory buildup"""
        tracks_to_remove = []

        for track_id, history in self.track_histories.items():
            if history and current_frame - history[-1]['frame'] > max_age_frames:
                tracks_to_remove.append(track_id)

        for track_id in tracks_to_remove:
            self.track_histories.pop(track_id, None)
            self.track_qualities.pop(track_id, None)
            self.track_positions.pop(track_id, None)
            self.confirmed_tracks.discard(track_id)

class FrameComparator:
    def __init__(self, history_size=30):
        self.frame_data = []
        self.history_size = history_size
        self.recent_counts = deque(maxlen=history_size)
        
    def add_frame_data(self, frame_num, people_count, unique_count, confidence_scores, bbox_data, inference_time):
        frame_info = {
            'frame_number': frame_num,
            'people_detected': people_count,
            'unique_people': unique_count,
            'confidence_scores': confidence_scores,
            'bounding_boxes': bbox_data,
            'inference_time': inference_time,
            'timestamp': time.time()
        }
        
        self.frame_data.append(frame_info)
        self.recent_counts.append(people_count)
        
        return self.analyze_frame_changes(frame_info)
    
    def analyze_frame_changes(self, current_frame):
        if len(self.frame_data) < 2:
            return {
                'count_change': 0,
                'trend': 'stable',
                'avg_recent': current_frame['people_detected'],
                'confidence_change': 0
            }
        
        prev_frame = self.frame_data[-2]
        count_change = current_frame['people_detected'] - prev_frame['people_detected']
        
        # Calculate trend over recent frames
        if len(self.recent_counts) >= 3:
            recent_avg = sum(list(self.recent_counts)[-5:]) / min(5, len(self.recent_counts))
            if recent_avg > current_frame['people_detected'] + 0.5:
                trend = 'decreasing'
            elif recent_avg < current_frame['people_detected'] - 0.5:
                trend = 'increasing'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        # Calculate confidence change
        prev_confidences = prev_frame.get('confidence_scores', [])
        curr_confidences = current_frame.get('confidence_scores', [])
        
        confidence_change = 0
        if prev_confidences and curr_confidences:
            avg_prev_conf = sum(prev_confidences) / len(prev_confidences)
            avg_curr_conf = sum(curr_confidences) / len(curr_confidences)
            confidence_change = avg_curr_conf - avg_prev_conf
        
        return {
            'count_change': count_change,
            'trend': trend,
            'avg_recent': sum(self.recent_counts) / len(self.recent_counts),
            'confidence_change': confidence_change
        }
    
    def get_statistics(self):
        if not self.frame_data:
            return {}
        
        people_counts = [frame['people_detected'] for frame in self.frame_data]
        unique_counts = [frame['unique_people'] for frame in self.frame_data]
        
        return {
            'total_frames': len(self.frame_data),
            'max_people': max(people_counts),
            'min_people': min(people_counts),
            'avg_people': sum(people_counts) / len(people_counts),
            'max_unique': max(unique_counts),
            'current_people': people_counts[-1] if people_counts else 0,
            'current_unique': unique_counts[-1] if unique_counts else 0
        }
    
    def export_data(self, filename):
        with open(filename, 'w') as f:
            json.dump(self.frame_data, f, indent=2)
        print(f"Frame data exported to {filename}")

class PersonDetector:
    def __init__(self, model_name="yolov8n.pt", confidence_threshold=0.5, use_tracking=True,
                 min_track_duration=15, track_confidence_threshold=0.6, use_perspective=True,
                 frame_width=1920, frame_height=1080, use_geotracking=False,
                 initial_lat=0.0, initial_lon=0.0, initial_alt=0.0):
        self.confidence_threshold = confidence_threshold
        self.person_class_id = 0
        self.use_tracking = use_tracking and DEEPSORT_AVAILABLE
        self.use_perspective = use_perspective
        self.use_geotracking = use_geotracking and GEOTRACKING_AVAILABLE

        print(f"Loading {model_name} model now")
        self.model = YOLO(model_name)
        print("Model loaded successfully :)")

        # Initialize perspective mapper
        if self.use_perspective:
            self.perspective_mapper = PerspectiveMapper(frame_width, frame_height)
            self.perspective_mapper.set_default_perspective_points()
            print("Perspective mapper initialized with default points.")

            # Initialize real-time map
            self.real_time_map = RealTimeMap(perspective_mapper=self.perspective_mapper)
            print("Real-time map initialized.")
        else:
            self.perspective_mapper = None
            self.real_time_map = None

        if self.use_tracking:
            # Enhanced DeepSORT configuration for better person tracking
            self.tracker = DeepSort(
                max_age=50,  # Keep tracks longer to handle temporary occlusions
                n_init=3,    # Require fewer frames to confirm a track (faster response)
                max_iou_distance=0.7,  # IoU threshold for track association
                max_cosine_distance=0.3,  # Appearance similarity threshold
                nn_budget=100,  # Limit appearance feature budget
                embedder="mobilenet",  # Use MobileNet for person re-identification
                embedder_gpu=True,  # Use GPU for embedder if available
                nms_max_overlap=0.8  # Non-max suppression threshold
            )
            print("Enhanced DeepSORT tracker initialized with optimized parameters.")

            # Initialize track quality manager with perspective mapper
            self.track_manager = TrackQualityManager(
                min_track_duration=min_track_duration,
                confidence_threshold=track_confidence_threshold,
                max_track_distance=100,
                track_merge_threshold=0.7,
                perspective_mapper=self.perspective_mapper
            )
            print(f"Track quality manager initialized (min_duration={min_track_duration}, confidence={track_confidence_threshold}).")
        else:
            self.tracker = None
            self.track_manager = None

        # Initialize geotracking system
        if self.use_geotracking:
            self.geotracker = GeoTracker(initial_lat, initial_lon, initial_alt)
            print("Geotracking system initialized.")
        else:
            self.geotracker = None

    def set_perspective_points(self, src_points: List[Tuple[int, int]]):
        """Set custom perspective transformation points"""
        if self.perspective_mapper:
            self.perspective_mapper.set_custom_perspective_points(np.array(src_points))
            print("Custom perspective points set successfully.")

    def calibrate_perspective_interactive(self, frame: np.ndarray) -> bool:
        """Interactive perspective calibration - returns True if calibration was completed"""
        if not self.perspective_mapper:
            return False

        print("Interactive Perspective Calibration")
        print("Click 4 points in order: Top-left, Top-right, Bottom-right, Bottom-left")
        print("Press 'r' to reset, 'c' to confirm, 'q' to quit")

        calibration_points = []

        def mouse_callback(event, x, y, flags, param):
            if event == cv2.EVENT_LBUTTONDOWN and len(calibration_points) < 4:
                calibration_points.append((x, y))
                print(f"Point {len(calibration_points)}: ({x}, {y})")

        cv2.namedWindow("Perspective Calibration", cv2.WINDOW_NORMAL)
        cv2.setMouseCallback("Perspective Calibration", mouse_callback)

        while True:
            display_frame = frame.copy()

            # Draw existing points
            for i, point in enumerate(calibration_points):
                cv2.circle(display_frame, point, 8, (0, 255, 0), -1)
                cv2.putText(display_frame, f'{i+1}', (point[0]-10, point[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # Draw lines between points
            if len(calibration_points) > 1:
                for i in range(len(calibration_points)):
                    start = calibration_points[i]
                    end = calibration_points[(i+1) % len(calibration_points)]
                    if i < len(calibration_points) - 1 or len(calibration_points) == 4:
                        cv2.line(display_frame, start, end, (255, 0, 0), 2)

            # Show instructions
            cv2.putText(display_frame, f"Points: {len(calibration_points)}/4", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(display_frame, "r: reset, c: confirm, q: quit", (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            cv2.imshow("Perspective Calibration", display_frame)

            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                cv2.destroyWindow("Perspective Calibration")
                return False
            elif key == ord('r'):
                calibration_points.clear()
                print("Points reset")
            elif key == ord('c') and len(calibration_points) == 4:
                self.set_perspective_points(calibration_points)
                cv2.destroyWindow("Perspective Calibration")
                return True
    
    def detect_people_in_frame(self, frame):
        start_time = time.time()
        
        results = self.model.predict(
            frame, 
            conf=self.confidence_threshold,
            classes=[self.person_class_id],
            verbose=False
        )
        
        inference_time = time.time() - start_time
        
        detections = results[0]
        people_count = len(detections.boxes) if detections.boxes is not None else 0
        
        return detections, people_count, inference_time
    
    def track_people(self, frame, detections, frame_num=0):
        if not self.use_tracking or detections.boxes is None or len(detections.boxes) == 0:
            return []

        # Prepare detection data for DeepSORT in the correct format
        raw_detections = []

        for box in detections.boxes:
            coords = np.array(box.xyxy[0].cpu().numpy(), dtype=np.float32).flatten()
            x1, y1, x2, y2 = coords[:4]
            conf = float(box.conf[0].cpu().numpy())

            # Filter out low confidence detections early
            if conf >= self.confidence_threshold:
                # DeepSORT expects format: ([left, top, width, height], confidence, class_name)
                bbox_ltwh = [float(x1), float(y1), float(x2 - x1), float(y2 - y1)]
                raw_detections.append((bbox_ltwh, conf, "person"))

        if not raw_detections:
            return []

        # Update DeepSORT tracker with properly formatted detections
        tracks = self.tracker.update_tracks(raw_detections, frame=frame)

        # Process tracks and update quality manager
        track_results = []
        for track in tracks:
            if not track.is_confirmed():
                continue

            track_id = track.track_id
            ltrb = track.to_ltrb()

            # Convert ltrb back to ltwh for consistency
            x1, y1, x2, y2 = ltrb
            bbox_ltwh = [float(x1), float(y1), float(x2 - x1), float(y2 - y1)]

            # Get detection confidence from track
            confidence = getattr(track, 'det_conf', 0.5)
            if hasattr(track, 'detection') and hasattr(track.detection, 'confidence'):
                confidence = track.detection.confidence

            # Ensure confidence is a valid float
            if confidence is None:
                confidence = 0.5
            confidence = float(confidence)

            # Ensure track_id is properly formatted
            if isinstance(track_id, str):
                # Extract numeric part if it's a string like "2024-01-01_123"
                try:
                    if '_' in str(track_id):
                        track_id = int(str(track_id).split('_')[-1])
                    else:
                        track_id = int(track_id)
                except (ValueError, AttributeError):
                    track_id = hash(str(track_id)) % 10000  # Convert to a reasonable integer

            track_info = {
                'track_id': track_id,
                'bbox': bbox_ltwh,
                'confidence': confidence
            }
            track_results.append(track_info)

            # Update track quality manager
            if self.track_manager:
                self.track_manager.update_track_history(
                    track_id, bbox_ltwh, confidence, frame_num
                )

        # Detect and merge duplicate tracks
        if self.track_manager and track_results:
            self.track_manager.detect_and_merge_duplicate_tracks(track_results)

            # Clean up old tracks periodically
            if frame_num % 100 == 0:
                self.track_manager.cleanup_old_tracks(frame_num)

        return track_results
    
    def draw_detections_on_frame(self, frame, detections, tracks=None, show_perspective=False):
        annotated_frame = frame.copy()

        # Draw perspective overlay if requested
        if show_perspective and self.perspective_mapper and self.perspective_mapper.is_calibrated:
            annotated_frame = self.perspective_mapper.draw_perspective_overlay(annotated_frame)

        # Draw YOLO detections in blue
        if detections.boxes is not None and len(detections.boxes) > 0:
            for box in detections.boxes:
                x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())
                confidence = float(box.conf[0].cpu().numpy())

                # Draw detection box in blue
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (255, 0, 0), 2)
                cv2.putText(annotated_frame, f'Person {confidence:.2f}',
                           (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

        # Draw tracking boxes with enhanced information
        if tracks:
            for track in tracks:
                # Convert bbox format if needed
                bbox = track['bbox']
                if len(bbox) == 4:
                    x1, y1, w, h = bbox
                    x2, y2 = x1 + w, y1 + h
                else:
                    x1, y1, x2, y2 = bbox

                track_id = track['track_id']
                confidence = track.get('confidence', 0.0)

                # Color coding based on track quality if available
                if hasattr(self, 'track_manager') and self.track_manager:
                    is_confirmed = self.track_manager.should_confirm_track(track_id)
                    quality = self.track_manager.track_qualities.get(track_id, 0.0)

                    if is_confirmed:
                        color = (0, 255, 0)  # Green for confirmed tracks
                        status = "CONFIRMED"
                    elif quality > 0.5:
                        color = (0, 255, 255)  # Yellow for good quality
                        status = f"Q:{quality:.2f}"
                    else:
                        color = (0, 165, 255)  # Orange for low quality
                        status = f"Q:{quality:.2f}"
                else:
                    color = (0, 255, 0)  # Default green
                    status = "TRACKED"

                # Draw tracking box
                cv2.rectangle(annotated_frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)

                # Draw track information
                cv2.putText(annotated_frame, f'ID: {track_id}', (int(x1), int(y1) - 50),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                cv2.putText(annotated_frame, status, (int(x1), int(y1) - 30),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

                # Show distance traveled if available
                if self.track_manager and track_id in self.track_manager.track_distances:
                    distance = self.track_manager.track_distances[track_id]
                    if self.perspective_mapper:
                        cv2.putText(annotated_frame, f'Dist: {distance:.1f}m', (int(x1), int(y1) - 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                    else:
                        cv2.putText(annotated_frame, f'Dist: {distance:.0f}px', (int(x1), int(y1) - 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        return annotated_frame

    def get_bird_eye_view(self, frame, tracks=None):
        """Get bird's-eye view of the scene with tracks"""
        try:
            if not self.perspective_mapper or not self.perspective_mapper.is_calibrated:
                return None

            # Transform frame to bird's-eye view
            bird_eye_frame = self.perspective_mapper.transform_frame(frame)

            # Update real-time map if available
            if self.real_time_map and tracks and len(tracks) > 0:
                # Set background if not set
                if self.real_time_map.background_map is None:
                    self.real_time_map.set_background_from_frame(frame)

                # Return the map image which should already be updated
                return self.real_time_map.map_image
            elif self.real_time_map:
                # Return map even without tracks
                return self.real_time_map.map_image

            return bird_eye_frame
        except Exception as e:
            print(f"Warning: Bird's-eye view generation failed: {e}")
            return None
    
    def get_detection_details(self, detections):
        detection_details = []
        confidence_scores = []
        
        if detections.boxes is not None:
            for box in detections.boxes:
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                confidence = float(box.conf[0].cpu().numpy())
                detection_details.append({
                    'bbox': [int(x1), int(y1), int(x2-x1), int(y2-y1)],
                    'confidence': confidence
                })
                confidence_scores.append(confidence)
        
        return detection_details, confidence_scores

def process_video_stream(detector, video_source, output_path=None, show_display=True, export_data=True,
                        show_perspective=False, show_bird_eye=False, calibrate_perspective=False):
    # Initialize frame comparator
    comparator = FrameComparator(history_size=60)  # Keep 60 frames of history
    
    if isinstance(video_source, str) and not video_source.isdigit():
        video_capture = cv2.VideoCapture(video_source)
        print(f"Processing video file: {video_source}")
    else:
        camera_index = int(video_source) if isinstance(video_source, str) else video_source
        video_capture = cv2.VideoCapture(camera_index)
        print(f"Using camera index: {camera_index}")
    
    if not video_capture.isOpened():
        print("Error: Could not open video source (T-T)")
        return
    
    frame_rate = int(video_capture.get(cv2.CAP_PROP_FPS)) or 30
    frame_width = int(video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frame_count = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Video properties: {frame_width}x{frame_height}, {frame_rate} fps")
    if total_frame_count > 0:
        print(f"Total frames: {total_frame_count}")
    
    video_writer = None
    if output_path:
        fourcc_codec = cv2.VideoWriter_fourcc(*"mp4v")
        video_writer = cv2.VideoWriter(output_path, fourcc_codec, frame_rate, (frame_width, frame_height))
        print(f"Output will be saved to: {output_path}")
    
    current_frame_number = 0
    total_detected_people = 0
    cumulative_inference_time = 0
    
    # Unique identity-based tracking with minimum frames
    track_id_frame_counts = {}
    confirmed_unique_ids = set()
    min_frames_for_unique = 10
    
    # Perspective calibration if requested
    if calibrate_perspective and detector.use_perspective:
        print("Starting perspective calibration...")
        ret, first_frame = video_capture.read()
        if ret:
            if detector.calibrate_perspective_interactive(first_frame):
                print("Perspective calibration completed successfully!")
            else:
                print("Perspective calibration cancelled.")
        video_capture.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Reset to beginning

    print("Starting detection with frame-by-frame comparison and perspective mapping")
    print("="*60)

    # Initialize bird's-eye view window if requested
    if show_bird_eye and detector.use_perspective:
        cv2.namedWindow("Bird's Eye View", cv2.WINDOW_NORMAL)
        cv2.resizeWindow("Bird's Eye View", 800, 600)

    while True:
        frame_available, current_frame = video_capture.read()
        if not frame_available:
            break
        
        current_frame_number += 1
        
        detections, frame_people_count, frame_inference_time = detector.detect_people_in_frame(current_frame)
        tracks = detector.track_people(current_frame, detections, current_frame_number) if detector.use_tracking else None

        # Get detection details
        detection_details, confidence_scores = detector.get_detection_details(detections)

        # Enhanced unique person counting using track quality manager
        if detector.use_tracking and detector.track_manager:
            # Get confirmed unique count from track quality manager
            unique_people_count = detector.track_manager.get_confirmed_unique_count()

            # Also maintain the old system for comparison (optional)
            if tracks:
                for track in tracks:
                    track_id = track['track_id']
                    if track_id not in track_id_frame_counts:
                        track_id_frame_counts[track_id] = 1
                    else:
                        track_id_frame_counts[track_id] += 1

                    if (track_id_frame_counts[track_id] == min_frames_for_unique and
                        track_id not in confirmed_unique_ids):
                        confirmed_unique_ids.add(track_id)
        else:
            # Fallback to old method if enhanced tracking is not available
            if tracks:
                for track in tracks:
                    track_id = track['track_id']
                    if track_id not in track_id_frame_counts:
                        track_id_frame_counts[track_id] = 1
                    else:
                        track_id_frame_counts[track_id] += 1

                    if (track_id_frame_counts[track_id] == min_frames_for_unique and
                        track_id not in confirmed_unique_ids):
                        confirmed_unique_ids.add(track_id)

            unique_people_count = len(confirmed_unique_ids)
        
        # Add frame data to comparator and get analysis
        comparison_analysis = comparator.add_frame_data(
            current_frame_number, frame_people_count, unique_people_count,
            confidence_scores, detection_details, frame_inference_time
        )
        
        # Get overall statistics
        stats = comparator.get_statistics()
        
        # Update real-time map if available
        if detector.real_time_map and tracks and len(tracks) > 0:
            try:
                detector.real_time_map.update_tracks(tracks, current_frame_number)
            except Exception as e:
                print(f"Warning: Real-time map update failed: {e}")

        annotated_frame = detector.draw_detections_on_frame(
            current_frame, detections, tracks, show_perspective
        )
        
        # Enhanced frame statistics with comparison data and tracking info
        frame_statistics = [
            f"Frame: {current_frame_number}" + (f"/{total_frame_count}" if total_frame_count > 0 else ""),
            f"Current People: {frame_people_count} | Unique: {unique_people_count}",
            f"Change: {comparison_analysis['count_change']:+d} | Trend: {comparison_analysis['trend']}",
            f"Recent Avg: {comparison_analysis['avg_recent']:.1f}",
            f"Max/Min: {stats.get('max_people', 0)}/{stats.get('min_people', 0)}",
        ]

        # Add tracking quality information if available
        if detector.use_tracking and detector.track_manager and tracks:
            active_tracks = len(tracks)
            confirmed_tracks = len([t for t in tracks if detector.track_manager.should_confirm_track(t['track_id'])])
            frame_statistics.append(f"Active Tracks: {active_tracks} | Confirmed: {confirmed_tracks}")

        # Add perspective mapping information
        if detector.use_perspective and detector.perspective_mapper and detector.perspective_mapper.is_calibrated:
            frame_statistics.append("Perspective: CALIBRATED")
        elif detector.use_perspective:
            frame_statistics.append("Perspective: DEFAULT")

        frame_statistics.append(
            f"Inference: {frame_inference_time:.3f}s | FPS: {1/frame_inference_time:.1f}" if frame_inference_time > 0 else "Calculating FPS..."
        )
        
        # Color coding for count changes
        text_color = (0, 255, 255)  # Yellow default
        if comparison_analysis['count_change'] > 0:
            text_color = (0, 255, 0)  # Green for increase
        elif comparison_analysis['count_change'] < 0:
            text_color = (0, 0, 255)  # Red for decrease
        
        text_y_position = 30
        for i, statistic_text in enumerate(frame_statistics):
            color = text_color if i == 2 else (0, 255, 255)  # Highlight change line
            cv2.putText(annotated_frame, statistic_text, (10, text_y_position), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            text_y_position += 25
        
        # Real-time console output for comparison with enhanced tracking info
        if current_frame_number % 5 == 0 or comparison_analysis['count_change'] != 0:
            base_info = (f"Frame {current_frame_number:4d}: {frame_people_count:2d} people "
                        f"({comparison_analysis['count_change']:+2d}) | "
                        f"Unique: {unique_people_count:2d} | "
                        f"Trend: {comparison_analysis['trend']:>10s} | "
                        f"Avg: {comparison_analysis['avg_recent']:4.1f}")

            # Add tracking quality info if available
            if detector.use_tracking and detector.track_manager and tracks:
                active_tracks = len(tracks)
                confirmed_tracks = len([t for t in tracks if detector.track_manager.should_confirm_track(t['track_id'])])
                tracking_info = f" | Tracks: {active_tracks}/{confirmed_tracks}"
                print(base_info + tracking_info)
            else:
                print(base_info)
        
        total_detected_people += frame_people_count
        cumulative_inference_time += frame_inference_time
        
        if video_writer:
            video_writer.write(annotated_frame)
        
        if show_display:
            cv2.imshow("Person Detection with Frame Comparison", annotated_frame)

            # Show bird's-eye view if requested
            if show_bird_eye and detector.use_perspective:
                bird_eye_view = detector.get_bird_eye_view(current_frame, tracks)
                if bird_eye_view is not None:
                    # Add statistics overlay to bird's-eye view
                    bird_eye_display = bird_eye_view.copy()

                    # Add map statistics
                    if detector.real_time_map:
                        map_stats = detector.real_time_map.get_statistics()
                        stats_text = [
                            f"Total Tracks: {map_stats['total_tracks']}",
                            f"Active Tracks: {map_stats['active_tracks']}",
                        ]

                        # Add zone counts if available
                        if map_stats['zone_counts']:
                            for zone_name, count in map_stats['zone_counts'].items():
                                stats_text.append(f"{zone_name}: {count}")

                        # Draw statistics
                        for i, text in enumerate(stats_text):
                            cv2.putText(bird_eye_display, text, (10, 30 + i * 25),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                    cv2.imshow("Bird's Eye View", bird_eye_display)

            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('p') and detector.use_perspective:
                # Toggle perspective overlay
                show_perspective = not show_perspective
                print(f"Perspective overlay: {'ON' if show_perspective else 'OFF'}")
            elif key == ord('c') and detector.use_perspective:
                # Recalibrate perspective
                if detector.calibrate_perspective_interactive(current_frame):
                    print("Perspective recalibrated successfully!")
        
        # Detailed progress every 30 frames
        if current_frame_number % 30 == 0:
            average_fps = current_frame_number / cumulative_inference_time if cumulative_inference_time > 0 else 0
            print(f"\n--- Progress Update (Frame {current_frame_number}) ---")
            print(f"Average FPS: {average_fps:.1f}")
            print(f"Max people seen: {stats.get('max_people', 0)}")
            print(f"Current trend: {comparison_analysis['trend']}")
            print("-" * 50)
    
    video_capture.release()
    if video_writer:
        video_writer.release()
    if show_display:
        cv2.destroyAllWindows()
    
    # Export frame comparison data
    if export_data:
        timestamp = int(time.time())
        json_filename = f"frame_comparison_data_{timestamp}.json"
        comparator.export_data(json_filename)
        
        # Create a simple plot of people count over time
        try:
            frame_numbers = [frame['frame_number'] for frame in comparator.frame_data]
            people_counts = [frame['people_detected'] for frame in comparator.frame_data]
            unique_counts = [frame['unique_people'] for frame in comparator.frame_data]
            
            plt.figure(figsize=(12, 6))
            plt.plot(frame_numbers, people_counts, label='People Detected', linewidth=2)
            plt.plot(frame_numbers, unique_counts, label='Unique People', linewidth=2)
            plt.xlabel('Frame Number')
            plt.ylabel('People Count')
            plt.title('People Detection Over Time')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            plot_filename = f"people_count_plot_{timestamp}.png"
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            print(f"Plot saved as {plot_filename}")
            
        except Exception as e:
            print(f"Could not create plot: {e}")
    
    # Final summary with enhanced tracking and perspective information
    final_stats = comparator.get_statistics()
    print("\n" + "="*60)
    print("ENHANCED PERSON TRACKING WITH PERSPECTIVE MAPPING SUMMARY")
    print("="*60)
    print(f"Total frames processed: {current_frame_number}")
    print(f"Maximum people in any frame: {final_stats.get('max_people', 0)}")
    print(f"Minimum people in any frame: {final_stats.get('min_people', 0)}")
    print(f"Average people per frame: {final_stats.get('avg_people', 0):.2f}")
    print(f"Final frame people count: {final_stats.get('current_people', 0)}")

    # Enhanced tracking statistics
    if detector.use_tracking and detector.track_manager:
        confirmed_unique = detector.track_manager.get_confirmed_unique_count()
        total_tracks = len(detector.track_manager.track_histories)
        merged_tracks = len(detector.track_manager.merged_tracks)

        print(f"Enhanced unique people count: {confirmed_unique}")
        print(f"Total tracks created: {total_tracks}")
        print(f"Tracks merged (duplicates): {merged_tracks}")
        print(f"Track confirmation rate: {confirmed_unique/total_tracks*100:.1f}%" if total_tracks > 0 else "Track confirmation rate: N/A")

        # Show track quality distribution
        if detector.track_manager.track_qualities:
            qualities = list(detector.track_manager.track_qualities.values())
            avg_quality = sum(qualities) / len(qualities)
            high_quality_tracks = len([q for q in qualities if q >= 0.7])
            print(f"Average track quality: {avg_quality:.2f}")
            print(f"High quality tracks (≥0.7): {high_quality_tracks}/{len(qualities)}")

        # Perspective and spatial statistics
        if detector.use_perspective and detector.track_manager.track_distances:
            distances = list(detector.track_manager.track_distances.values())
            if distances:
                total_distance = sum(distances)
                avg_distance = total_distance / len(distances)
                max_distance = max(distances)

                if detector.perspective_mapper and detector.perspective_mapper.is_calibrated:
                    print(f"Total distance traveled: {total_distance:.1f} meters")
                    print(f"Average distance per track: {avg_distance:.1f} meters")
                    print(f"Maximum distance by single track: {max_distance:.1f} meters")
                else:
                    print(f"Total distance traveled: {total_distance:.0f} pixels")
                    print(f"Average distance per track: {avg_distance:.0f} pixels")
                    print(f"Maximum distance by single track: {max_distance:.0f} pixels")
    else:
        print(f"Basic unique people count: {final_stats.get('max_unique', 0)}")

    # Perspective mapping statistics
    if detector.use_perspective:
        if detector.perspective_mapper and detector.perspective_mapper.is_calibrated:
            print("Perspective transformation: CALIBRATED")
        else:
            print("Perspective transformation: DEFAULT POINTS")

        # Real-time map statistics
        if detector.real_time_map:
            map_stats = detector.real_time_map.get_statistics()
            print(f"Map tracks processed: {map_stats['total_tracks']}")
            if map_stats['zone_counts']:
                print("Zone statistics:")
                for zone_name, count in map_stats['zone_counts'].items():
                    print(f"  {zone_name}: {count} people")

    if current_frame_number > 0:
        print(f"Total people detections: {total_detected_people}")
        print(f"Average inference time: {cumulative_inference_time/current_frame_number:.3f}s")
        print(f"Average processing FPS: {current_frame_number/cumulative_inference_time:.1f}")

    print("="*60)
    
    return comparator

# Main execution
if __name__ == "__main__":
    argument_parser = argparse.ArgumentParser(description="YOLOv8 Person Detection with Frame Comparison")
    argument_parser.add_argument("--model", default="yolov8n.pt", help="YOLOv8 model name")
    argument_parser.add_argument("--input", required=True, help="Path to input video or camera index (0)")
    argument_parser.add_argument("--output", help="Path to output video file")
    argument_parser.add_argument("--confidence", type=float, default=0.5, help="Confidence threshold")
    argument_parser.add_argument("--no-display", action="store_true", help="Don't display video window")
    argument_parser.add_argument("--no-tracking", action="store_true", help="Disable DeepSORT tracking")
    argument_parser.add_argument("--no-export", action="store_true", help="Don't export frame data")
    argument_parser.add_argument("--min-track-duration", type=int, default=15, help="Minimum frames for track confirmation")
    argument_parser.add_argument("--track-confidence", type=float, default=0.6, help="Minimum confidence for track quality")
    argument_parser.add_argument("--no-perspective", action="store_true", help="Disable perspective transformation")
    argument_parser.add_argument("--show-perspective", action="store_true", help="Show perspective overlay on video")
    argument_parser.add_argument("--show-bird-eye", action="store_true", help="Show bird's-eye view window")
    argument_parser.add_argument("--calibrate-perspective", action="store_true", help="Interactive perspective calibration")

    command_line_args = argument_parser.parse_args()

    person_detector = PersonDetector(
        model_name=command_line_args.model,
        confidence_threshold=command_line_args.confidence,
        use_tracking=not command_line_args.no_tracking,
        min_track_duration=command_line_args.min_track_duration,
        track_confidence_threshold=command_line_args.track_confidence,
        use_perspective=not command_line_args.no_perspective
    )

    input_video_source = command_line_args.input
    if input_video_source.isdigit():
        input_video_source = int(input_video_source)

    comparator = process_video_stream(
        detector=person_detector,
        video_source=input_video_source,
        output_path=command_line_args.output,
        show_display=not command_line_args.no_display,
        export_data=not command_line_args.no_export,
        show_perspective=command_line_args.show_perspective,
        show_bird_eye=command_line_args.show_bird_eye,
        calibrate_perspective=command_line_args.calibrate_perspective
    )

    print("\nFrame comparison data available in the returned comparator object.")
    print("Use comparator.frame_data to access detailed frame-by-frame information.")

'''
Enhanced Person Tracking with Perspective Mapping Usage Examples:

Basic webcam with enhanced tracking and perspective mapping:
python obj_counter.py --input 0

Video file with bird's-eye view and perspective overlay:
python obj_counter.py --input video.mp4 --show-bird-eye --show-perspective

Interactive perspective calibration:
python obj_counter.py --input 0 --calibrate-perspective --show-bird-eye

High accuracy mode with spatial analysis:
python obj_counter.py --input video.mp4 --confidence 0.8 --min-track-duration 25 --track-confidence 0.8 --show-bird-eye

Real-time monitoring with all features:
python obj_counter.py --input 0 --show-perspective --show-bird-eye --min-track-duration 15

Disable perspective for basic tracking only:
python obj_counter.py --input video.mp4 --no-perspective --no-display

Enhanced Features:
- Advanced person tracking with DeepSORT
- Perspective transformation and bird's-eye view mapping
- Real-time spatial analysis and movement tracking
- Interactive perspective calibration
- Track quality assessment and confirmation
- Duplicate track detection and merging
- Movement pattern analysis with real-world distances
- Confidence-based track validation
- Real-time frame-by-frame comparison
- Trend analysis (increasing/decreasing/stable)
- JSON export of all frame data
- Automatic plot generation
- Enhanced console output with tracking and spatial statistics
- Color-coded display for track quality
- Configurable tracking and perspective parameters

Tracking Parameters:
--min-track-duration: Minimum frames required to confirm a unique person (default: 15)
--track-confidence: Minimum confidence threshold for track quality assessment (default: 0.6)
--confidence: Detection confidence threshold (default: 0.5)
--no-tracking: Disable tracking entirely for basic detection only

Perspective Parameters:
--no-perspective: Disable perspective transformation and mapping
--show-perspective: Show perspective overlay on main video window
--show-bird-eye: Show bird's-eye view window with real-time map
--calibrate-perspective: Interactive perspective calibration at startup

Interactive Controls (during runtime):
- Press 'q' to quit
- Press 'p' to toggle perspective overlay
- Press 'c' to recalibrate perspective transformation
'''