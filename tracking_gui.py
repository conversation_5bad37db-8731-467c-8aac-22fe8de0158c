#!/usr/bin/env python3
"""
Enhanced Person Tracking GUI Application
Real-time video processing with perspective mapping and tracking analytics
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
from collections import deque
import json
import os
from datetime import datetime

# Import our enhanced tracking system
from obj_counter import PersonDetector, FrameComparator
from live_grid_map import LiveGridMap

# Check for geotracking availability
try:
    from geotracking_system import GeoTracker
    GEOTRACKING_AVAILABLE = True
except ImportError:
    GEOTRACKING_AVAILABLE = False

class TrackingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Person Tracking System")

        # Make window responsive to screen size
        self.setup_responsive_window()
        self.root.configure(bg='#2b2b2b')
        
        # Initialize variables
        self.detector = None
        self.video_capture = None
        self.is_running = False
        self.is_paused = False
        self.current_frame = None
        self.bird_eye_frame = None
        self.grid_map_frame = None
        self.frame_count = 0
        self.fps_counter = deque(maxlen=30)
        self.comparator = FrameComparator(history_size=60)

        # Initialize live grid map
        self.grid_map = LiveGridMap(width=400, height=300, grid_size=20)

        # Initialize geotracking if available
        self.geotracker = None
        self.gps_simulator = None
        self.use_gps_simulation = True  # Enable GPS simulation by default
        if GEOTRACKING_AVAILABLE:
            from geotracking_system import GeoTracker, GPSSimulator
            self.geotracker = GeoTracker(40.7128, -74.0060, 50.0)  # Default NYC coordinates
            self.gps_simulator = GPSSimulator(40.7128, -74.0060, 50.0)
            print("🌍 Geotracking system initialized with GPS simulation")
        
        # Tracking statistics
        self.stats = {
            'current_people': 0,
            'unique_people': 0,
            'max_people': 0,
            'total_frames': 0,
            'active_tracks': 0,
            'confirmed_tracks': 0,
            'avg_fps': 0.0,
            'trend': 'stable',
            'grid_density': 0.0,
            # Geotracking stats
            'drone_lat': 0.0,
            'drone_lon': 0.0,
            'drone_alt': 0.0,
            'flight_distance': 0.0,
            'people_tracked_geo': 0
        }
        
        # Setup GUI
        self.setup_gui()
        self.setup_styles()
        
        # Initialize detector with default settings
        self.initialize_detector()

    def setup_responsive_window(self):
        """Setup responsive window sizing based on screen dimensions"""
        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Calculate optimal window size (80% of screen, with minimums)
        min_width, min_height = 1200, 700
        max_width, max_height = int(screen_width * 0.9), int(screen_height * 0.9)

        # Set window size
        window_width = max(min_width, min(1600, max_width))
        window_height = max(min_height, min(900, max_height))

        # Center window on screen
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(min_width, min_height)

        # Make window resizable
        self.root.resizable(True, True)

        # Store dimensions for responsive layout
        self.window_width = window_width
        self.window_height = window_height

        # Bind resize event
        self.root.bind('<Configure>', self.on_window_resize)

    def on_window_resize(self, event):
        """Handle window resize events"""
        if event.widget == self.root:
            # Update stored dimensions
            self.window_width = event.width
            self.window_height = event.height

            # Adjust video display sizes based on new window size
            self.adjust_video_sizes()

    def setup_styles(self):
        """Setup custom styles for the GUI"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Stat.TLabel', font=('Arial', 12, 'bold'), background='#3b3b3b', foreground='#00ff00')
        style.configure('Info.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='white')
        style.configure('Control.TButton', font=('Arial', 10, 'bold'))
        
    def setup_gui(self):
        """Setup the main GUI layout with responsive design"""
        # Create main scrollable container
        self.setup_scrollable_container()

        # Top control panel
        self.setup_control_panel(self.main_frame)

        # Video display area
        self.setup_video_area(self.main_frame)

        # Statistics panel
        self.setup_stats_panel(self.main_frame)

        # Bottom status bar
        self.setup_status_bar(self.main_frame)

    def setup_scrollable_container(self):
        """Setup scrollable main container"""
        # Create canvas and scrollbar for scrolling
        self.canvas = tk.Canvas(self.root, bg='#2b2b2b', highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#2b2b2b')

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        # Create window in canvas
        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # Pack canvas and scrollbar
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # Bind canvas resize
        self.canvas.bind('<Configure>', self.on_canvas_configure)

        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)
        self.root.bind("<MouseWheel>", self.on_mousewheel)

        # Main frame inside scrollable area
        self.main_frame = tk.Frame(self.scrollable_frame, bg='#2b2b2b')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def on_canvas_configure(self, event):
        """Handle canvas resize"""
        # Update scrollable frame width to match canvas
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)

    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
    def setup_control_panel(self, parent):
        """Setup responsive control panel with buttons and settings"""
        control_frame = tk.Frame(parent, bg='#3b3b3b', relief=tk.RAISED, bd=2)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # Title
        title_label = ttk.Label(control_frame, text="🎯 Enhanced Person Tracking System", style='Title.TLabel')
        title_label.pack(pady=8)

        # Main controls container
        controls_container = tk.Frame(control_frame, bg='#3b3b3b')
        controls_container.pack(fill=tk.X, padx=10, pady=5)

        # Source selection frame
        source_frame = tk.LabelFrame(controls_container, text="📁 Video Source", bg='#3b3b3b', fg='white', font=('Arial', 9, 'bold'))
        source_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        source_buttons = tk.Frame(source_frame, bg='#3b3b3b')
        source_buttons.pack(padx=5, pady=5)

        ttk.Button(source_buttons, text="📁 File", command=self.select_video_file, style='Control.TButton').pack(pady=2)
        ttk.Button(source_buttons, text="📷 Webcam", command=self.use_webcam, style='Control.TButton').pack(pady=2)

        # Control buttons frame
        control_buttons_frame = tk.LabelFrame(controls_container, text="🎮 Controls", bg='#3b3b3b', fg='white', font=('Arial', 9, 'bold'))
        control_buttons_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10)

        buttons_grid = tk.Frame(control_buttons_frame, bg='#3b3b3b')
        buttons_grid.pack(padx=5, pady=5)

        self.start_button = ttk.Button(buttons_grid, text="▶️ Start", command=self.start_tracking, style='Control.TButton')
        self.start_button.grid(row=0, column=0, padx=2, pady=2, sticky='ew')

        self.pause_button = ttk.Button(buttons_grid, text="⏸️ Pause", command=self.pause_tracking, style='Control.TButton', state=tk.DISABLED)
        self.pause_button.grid(row=0, column=1, padx=2, pady=2, sticky='ew')

        self.stop_button = ttk.Button(buttons_grid, text="⏹️ Stop", command=self.stop_tracking, style='Control.TButton', state=tk.DISABLED)
        self.stop_button.grid(row=1, column=0, columnspan=2, padx=2, pady=2, sticky='ew')

        # Configure button grid
        buttons_grid.columnconfigure(0, weight=1)
        buttons_grid.columnconfigure(1, weight=1)

        # Settings frame
        settings_frame = tk.LabelFrame(controls_container, text="⚙️ Settings", bg='#3b3b3b', fg='white', font=('Arial', 9, 'bold'))
        settings_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0))

        settings_grid = tk.Frame(settings_frame, bg='#3b3b3b')
        settings_grid.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Confidence threshold
        tk.Label(settings_grid, text="Confidence:", bg='#3b3b3b', fg='white', font=('Arial', 8)).grid(row=0, column=0, sticky='w', padx=2)
        self.confidence_var = tk.DoubleVar(value=0.6)
        confidence_scale = tk.Scale(settings_grid, from_=0.1, to=1.0, resolution=0.1, orient=tk.HORIZONTAL,
                                  variable=self.confidence_var, bg='#3b3b3b', fg='white', highlightbackground='#3b3b3b', length=120)
        confidence_scale.grid(row=0, column=1, sticky='ew', padx=2)

        # Track duration
        tk.Label(settings_grid, text="Track Duration:", bg='#3b3b3b', fg='white', font=('Arial', 8)).grid(row=1, column=0, sticky='w', padx=2)
        self.track_duration_var = tk.IntVar(value=15)
        duration_scale = tk.Scale(settings_grid, from_=5, to=30, orient=tk.HORIZONTAL,
                                variable=self.track_duration_var, bg='#3b3b3b', fg='white', highlightbackground='#3b3b3b', length=120)
        duration_scale.grid(row=1, column=1, sticky='ew', padx=2)

        # Checkboxes
        self.show_perspective_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_grid, text="🔄 Perspective", variable=self.show_perspective_var,
                      bg='#3b3b3b', fg='white', selectcolor='#3b3b3b', font=('Arial', 8)).grid(row=2, column=0, sticky='w', padx=2)

        self.show_bird_eye_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_grid, text="🗺️ Bird's Eye", variable=self.show_bird_eye_var,
                      bg='#3b3b3b', fg='white', selectcolor='#3b3b3b', font=('Arial', 8)).grid(row=2, column=1, sticky='w', padx=2)

        # GPS/Geotracking controls
        if GEOTRACKING_AVAILABLE:
            self.enable_gps_var = tk.BooleanVar(value=True)
            tk.Checkbutton(settings_grid, text="🌍 GPS Tracking", variable=self.enable_gps_var,
                          bg='#3b3b3b', fg='white', selectcolor='#3b3b3b', font=('Arial', 8)).grid(row=3, column=0, sticky='w', padx=2)

            # GPS buttons
            gps_buttons = tk.Frame(settings_grid, bg='#3b3b3b')
            gps_buttons.grid(row=3, column=1, sticky='ew', padx=2)

            ttk.Button(gps_buttons, text="🗺️ Map", command=self.open_gps_map, style='Control.TButton').pack(side=tk.LEFT, padx=(0, 2))
            ttk.Button(gps_buttons, text="💾 Export", command=self.export_gps_data, style='Control.TButton').pack(side=tk.LEFT, padx=2)

        # Configure settings grid
        settings_grid.columnconfigure(1, weight=1)
        
    def setup_video_area(self, parent):
        """Setup responsive video display area"""
        video_frame = tk.Frame(parent, bg='#2b2b2b')
        video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Main video display
        main_video_frame = tk.Frame(video_frame, bg='#1b1b1b', relief=tk.SUNKEN, bd=2)
        main_video_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # Video header
        video_header = tk.Frame(main_video_frame, bg='#3b3b3b', height=30)
        video_header.pack(fill=tk.X)
        video_header.pack_propagate(False)

        tk.Label(video_header, text="📹 Main Video Feed", bg='#3b3b3b', fg='white', font=('Arial', 11, 'bold')).pack(side=tk.LEFT, padx=10, pady=5)

        # GPS coordinates display
        self.gps_display_label = tk.Label(video_header, text="🌍 GPS: Initializing...", bg='#3b3b3b', fg='#00d4aa', font=('Arial', 9))
        self.gps_display_label.pack(side=tk.RIGHT, padx=10, pady=5)

        # Video display with minimum size
        self.main_video_label = tk.Label(main_video_frame, bg='black', text="No Video Source\nSelect a video file or webcam to begin",
                                       fg='white', font=('Arial', 12))
        self.main_video_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Right panel for additional views
        right_panel = tk.Frame(video_frame, bg='#1b1b1b', width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_panel.pack_propagate(False)

        # Bird's eye view display
        bird_eye_frame = tk.Frame(right_panel, bg='#1b1b1b', relief=tk.SUNKEN, bd=2)
        bird_eye_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 3))

        bird_header = tk.Frame(bird_eye_frame, bg='#3b3b3b', height=25)
        bird_header.pack(fill=tk.X)
        bird_header.pack_propagate(False)

        tk.Label(bird_header, text="🗺️ Bird's Eye View", bg='#3b3b3b', fg='white', font=('Arial', 9, 'bold')).pack(pady=3)

        self.bird_eye_label = tk.Label(bird_eye_frame, bg='black', text="Bird's Eye View\nWill appear during tracking",
                                     fg='gray', font=('Arial', 8))
        self.bird_eye_label.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

        # Grid map display
        grid_map_frame = tk.Frame(right_panel, bg='#1b1b1b', relief=tk.SUNKEN, bd=2)
        grid_map_frame.pack(fill=tk.BOTH, expand=True, pady=(3, 0))

        grid_header = tk.Frame(grid_map_frame, bg='#3b3b3b', height=25)
        grid_header.pack(fill=tk.X)
        grid_header.pack_propagate(False)

        tk.Label(grid_header, text="🎯 Live Grid Map", bg='#3b3b3b', fg='white', font=('Arial', 9, 'bold')).pack(pady=3)

        self.grid_map_label = tk.Label(grid_map_frame, bg='black', text="Tactical Grid Map\nWill appear during tracking",
                                     fg='gray', font=('Arial', 8))
        self.grid_map_label.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

    def adjust_video_sizes(self):
        """Adjust video display sizes based on current window size"""
        if hasattr(self, 'main_video_label'):
            # Calculate optimal video size based on window dimensions
            available_width = max(400, self.window_width - 400)  # Leave space for right panel
            available_height = max(300, self.window_height - 300)  # Leave space for controls and stats

            # Maintain aspect ratio
            video_width = min(available_width, int(available_height * 4/3))
            video_height = min(available_height, int(available_width * 3/4))

            # Update video label size (this will be used in update_display)
            self.video_display_size = (video_width, video_height)
        
    def setup_stats_panel(self, parent):
        """Setup responsive statistics panel"""
        stats_frame = tk.Frame(parent, bg='#3b3b3b', relief=tk.RAISED, bd=2)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        # Statistics header
        stats_header = tk.Frame(stats_frame, bg='#3b3b3b')
        stats_header.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(stats_header, text="📊 Real-time Statistics", bg='#3b3b3b', fg='white', font=('Arial', 12, 'bold')).pack(side=tk.LEFT)

        # Create responsive statistics grid
        stats_container = tk.Frame(stats_frame, bg='#3b3b3b')
        stats_container.pack(fill=tk.X, padx=10, pady=(0, 10))

        # Statistics labels with more compact layout
        self.stat_labels = {}
        stats_config = [
            ('👥 Current', 'current_people', '#00ff88'),
            ('🎯 Unique', 'unique_people', '#00d4aa'),
            ('📈 Max', 'max_people', '#ff6b6b'),
            ('🔄 Active', 'active_tracks', '#4ecdc4'),
            ('✅ Confirmed', 'confirmed_tracks', '#45b7d1'),
            ('⚡ FPS', 'avg_fps', '#96ceb4'),
            ('🎬 Frames', 'total_frames', '#ff9ff3'),
            ('📊 Trend', 'trend', '#feca57'),
            ('🎯 Grid %', 'grid_density', '#a8e6cf'),
        ]

        # Add geotracking stats if available
        if GEOTRACKING_AVAILABLE:
            geo_stats = [
                ('🌍 Lat', 'drone_lat', '#ff6b9d'),
                ('🌍 Lon', 'drone_lon', '#c44569'),
                ('🚁 Alt', 'drone_alt', '#f8b500'),
                ('📏 Dist', 'flight_distance', '#00a8ff'),
                ('👥 Geo', 'people_tracked_geo', '#7bed9f'),
            ]
            stats_config.extend(geo_stats)

        # Create stats in a flowing layout
        stats_flow = tk.Frame(stats_container, bg='#3b3b3b')
        stats_flow.pack(fill=tk.X)

        for i, (label_text, key, color) in enumerate(stats_config):
            # Create compact stat card
            stat_card = tk.Frame(stats_flow, bg='#4b4b4b', relief=tk.RAISED, bd=1, width=120, height=60)
            stat_card.pack(side=tk.LEFT, padx=3, pady=3)
            stat_card.pack_propagate(False)

            # Stat label and value
            tk.Label(stat_card, text=label_text, bg='#4b4b4b', fg='white', font=('Arial', 8, 'bold')).pack(pady=(3, 0))
            stat_label = tk.Label(stat_card, text="0", bg='#4b4b4b', fg=color, font=('Arial', 11, 'bold'))
            stat_label.pack(pady=(0, 3))
            self.stat_labels[key] = stat_label

            # Start new row after every 5 items for better wrapping
            if (i + 1) % 5 == 0 and i < len(stats_config) - 1:
                stats_flow = tk.Frame(stats_container, bg='#3b3b3b')
                stats_flow.pack(fill=tk.X)

    def setup_status_bar(self, parent):
        """Setup the status bar"""
        self.status_frame = tk.Frame(parent, bg='#1b1b1b', relief=tk.SUNKEN, bd=1)
        self.status_frame.pack(fill=tk.X)

        self.status_label = tk.Label(self.status_frame, text="Ready - Select video source to begin",
                                   bg='#1b1b1b', fg='white', font=('Arial', 10))
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_frame, variable=self.progress_var, length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=10, pady=5)

    def initialize_detector(self):
        """Initialize the person detector with default settings"""
        try:
            # Initialize detector with geotracking if available
            use_geotracking = GEOTRACKING_AVAILABLE and hasattr(self, 'geotracker') and self.geotracker is not None

            self.detector = PersonDetector(
                confidence_threshold=self.confidence_var.get(),
                use_tracking=True,
                min_track_duration=self.track_duration_var.get(),
                track_confidence_threshold=0.6,
                use_perspective=True,
                use_geotracking=use_geotracking,
                initial_lat=40.7128,
                initial_lon=-74.0060,
                initial_alt=50.0
            )

            # Link geotracker to detector if available
            if use_geotracking and hasattr(self.detector, 'geotracker'):
                self.detector.geotracker = self.geotracker

            status_msg = "Detector initialized successfully"
            if use_geotracking:
                status_msg += " with geotracking"
            self.update_status(status_msg)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to initialize detector: {str(e)}")
            self.update_status(f"Error: {str(e)}")

    def select_video_file(self):
        """Select a video file for processing"""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.video_source = file_path
            self.update_status(f"Selected video: {os.path.basename(file_path)}")
            self.start_button.config(state=tk.NORMAL)

    def use_webcam(self):
        """Use webcam as video source"""
        self.video_source = 0
        self.update_status("Webcam selected as video source")
        self.start_button.config(state=tk.NORMAL)

    def start_tracking(self):
        """Start the tracking process"""
        if not hasattr(self, 'video_source'):
            messagebox.showwarning("Warning", "Please select a video source first")
            return

        try:
            # Update detector settings
            self.detector.confidence_threshold = self.confidence_var.get()
            if self.detector.track_manager:
                self.detector.track_manager.min_track_duration = self.track_duration_var.get()

            # Open video capture
            self.video_capture = cv2.VideoCapture(self.video_source)
            if not self.video_capture.isOpened():
                raise Exception("Failed to open video source")

            # Get video properties
            if isinstance(self.video_source, str):
                total_frames = int(self.video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = self.video_capture.get(cv2.CAP_PROP_FPS)
                self.progress_bar.config(maximum=total_frames)
                self.update_status(f"Video loaded: {total_frames} frames, {fps:.1f} FPS")
            else:
                self.progress_bar.config(maximum=100)
                self.update_status("Webcam started")

            # Start processing thread
            self.is_running = True
            self.is_paused = False
            self.frame_count = 0

            # Update button states
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)

            # Start processing in separate thread
            self.processing_thread = threading.Thread(target=self.process_video, daemon=True)
            self.processing_thread.start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start tracking: {str(e)}")
            self.update_status(f"Error: {str(e)}")

    def pause_tracking(self):
        """Pause/resume tracking"""
        self.is_paused = not self.is_paused
        if self.is_paused:
            self.pause_button.config(text="Resume")
            self.update_status("Tracking paused")
        else:
            self.pause_button.config(text="Pause")
            self.update_status("Tracking resumed")

    def stop_tracking(self):
        """Stop the tracking process"""
        self.is_running = False

        if self.video_capture:
            self.video_capture.release()
            self.video_capture = None

        # Update button states
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="Pause")
        self.stop_button.config(state=tk.DISABLED)

        self.update_status("Tracking stopped")

        # Show final statistics
        self.show_final_stats()

    def process_video(self):
        """Main video processing loop"""
        while self.is_running:
            if self.is_paused:
                time.sleep(0.1)
                continue

            ret, frame = self.video_capture.read()
            if not ret:
                if isinstance(self.video_source, str):  # Video file ended
                    self.root.after(0, self.stop_tracking)
                    break
                else:  # Webcam error
                    continue

            frame_start_time = time.time()

            try:
                # Update GPS simulation if enabled
                if self.geotracker and self.gps_simulator and self.enable_gps_var.get():
                    gps_data = self.gps_simulator.get_next_position()
                    self.geotracker.update_drone_position(
                        gps_data['latitude'],
                        gps_data['longitude'],
                        gps_data['altitude'],
                        gps_data['accuracy'],
                        gps_data['heading'],
                        gps_data['speed']
                    )

                # Process frame
                self.current_frame = frame.copy()
                self.frame_count += 1

                # Detect people
                detections, frame_people_count, inference_time = self.detector.detect_people_in_frame(frame)

                # Track people
                tracks = self.detector.track_people(frame, detections, self.frame_count) if self.detector.use_tracking else []

                # Get detection details for comparator
                detection_details, confidence_scores = self.detector.get_detection_details(detections)

                # Calculate unique count
                unique_count = 0
                if self.detector.use_tracking and self.detector.track_manager:
                    unique_count = self.detector.track_manager.get_confirmed_unique_count()

                # Prepare bbox data
                bbox_data = []
                if tracks:
                    bbox_data = [track['bbox'] for track in tracks]
                elif detection_details:
                    bbox_data = [det['bbox'] for det in detection_details]

                # Update comparator with correct arguments - add_frame_data returns the analysis
                comparison_analysis = self.comparator.add_frame_data(
                    self.frame_count,
                    frame_people_count,
                    unique_count,
                    confidence_scores,
                    bbox_data,
                    inference_time
                )

                # Update statistics
                self.update_statistics(frame_people_count, tracks, comparison_analysis, inference_time, unique_count)

                # Draw annotations
                annotated_frame = self.detector.draw_detections_on_frame(
                    frame, detections, tracks, self.show_perspective_var.get()
                )

                # Get bird's eye view if enabled
                bird_eye_frame = None
                if self.show_bird_eye_var.get():
                    bird_eye_frame = self.detector.get_bird_eye_view(frame, tracks)

                # Update grid map with current people positions
                grid_map_frame = None
                if tracks:
                    # Get frame dimensions
                    frame_height, frame_width = frame.shape[:2]
                    grid_map_frame = self.grid_map.update_people_positions(tracks, frame_width, frame_height)

                    # Track people GPS positions if geotracking is enabled
                    if self.geotracker and self.enable_gps_var.get():
                        for track in tracks:
                            # Get person center position
                            bbox = track['bbox']
                            center_x = bbox[0] + bbox[2] / 2
                            center_y = bbox[1] + bbox[3] / 2

                            # Track person's GPS position
                            self.geotracker.track_person_geo(
                                track['track_id'],
                                center_x,
                                center_y,
                                frame_width,
                                frame_height,
                                track.get('confidence', 0.8)
                            )

                # Update GUI in main thread
                self.root.after(0, self.update_display, annotated_frame, bird_eye_frame, grid_map_frame)

                # Calculate FPS
                frame_time = time.time() - frame_start_time
                self.fps_counter.append(1.0 / frame_time if frame_time > 0 else 0)

                # Update progress
                if isinstance(self.video_source, str):
                    self.progress_var.set(self.frame_count)

                # Control frame rate for webcam
                if isinstance(self.video_source, int):
                    time.sleep(0.033)  # ~30 FPS for webcam

            except Exception as e:
                print(f"Error processing frame {self.frame_count}: {e}")
                continue

    def update_statistics(self, frame_people_count, tracks, comparison_analysis, inference_time, unique_count=0):
        """Update tracking statistics"""
        # Basic stats
        self.stats['current_people'] = frame_people_count
        self.stats['total_frames'] = self.frame_count
        self.stats['trend'] = comparison_analysis.get('trend', 'stable')
        self.stats['inference_time'] = inference_time
        self.stats['unique_people'] = unique_count

        # Track stats
        if tracks:
            self.stats['active_tracks'] = len(tracks)
            if self.detector.track_manager:
                confirmed = len([t for t in tracks if self.detector.track_manager.should_confirm_track(t['track_id'])])
                self.stats['confirmed_tracks'] = confirmed

        # Max people
        if frame_people_count > self.stats['max_people']:
            self.stats['max_people'] = frame_people_count

        # FPS calculation
        if self.fps_counter:
            self.stats['avg_fps'] = sum(self.fps_counter) / len(self.fps_counter)

        # Grid map statistics
        if self.grid_map:
            grid_analytics = self.grid_map.get_grid_analytics()
            self.stats['grid_density'] = grid_analytics.get('density_percentage', 0)

        # Geotracking statistics
        if self.geotracker:
            flight_stats = self.geotracker.get_flight_statistics()
            self.stats['drone_lat'] = flight_stats['current_position']['latitude']
            self.stats['drone_lon'] = flight_stats['current_position']['longitude']
            self.stats['drone_alt'] = flight_stats['current_position']['altitude']
            self.stats['flight_distance'] = flight_stats['total_distance_flown']
            self.stats['people_tracked_geo'] = flight_stats['people_tracked']

    def update_display(self, main_frame, bird_eye_frame, grid_map_frame=None):
        """Update the video displays and statistics with responsive sizing"""
        try:
            # Update main video display with responsive sizing
            if main_frame is not None:
                # Get current window size for responsive display
                if hasattr(self, 'video_display_size'):
                    display_size = self.video_display_size
                else:
                    # Fallback size calculation
                    available_width = max(400, self.window_width - 400)
                    available_height = max(300, self.window_height - 350)
                    display_size = (min(800, available_width), min(600, available_height))

                # Resize frame to fit display
                display_frame = cv2.resize(main_frame, display_size)
                display_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)
                image = Image.fromarray(display_frame)
                photo = ImageTk.PhotoImage(image)
                self.main_video_label.configure(image=photo, text="")
                self.main_video_label.image = photo

            # Update bird's eye view display with responsive sizing
            if bird_eye_frame is not None and self.show_bird_eye_var.get():
                # Calculate responsive size for bird's eye view
                panel_width = min(340, max(250, self.window_width // 5))
                panel_height = min(200, max(150, self.window_height // 6))

                bird_display = cv2.resize(bird_eye_frame, (panel_width, panel_height))
                bird_display = cv2.cvtColor(bird_display, cv2.COLOR_BGR2RGB)
                bird_image = Image.fromarray(bird_display)
                bird_photo = ImageTk.PhotoImage(bird_image)
                self.bird_eye_label.configure(image=bird_photo, text="")
                self.bird_eye_label.image = bird_photo

            # Update grid map display with responsive sizing
            if grid_map_frame is not None:
                # Calculate responsive size for grid map
                panel_width = min(340, max(250, self.window_width // 5))
                panel_height = min(200, max(150, self.window_height // 6))

                grid_display = cv2.resize(grid_map_frame, (panel_width, panel_height))
                grid_display = cv2.cvtColor(grid_display, cv2.COLOR_BGR2RGB)
                grid_image = Image.fromarray(grid_display)
                grid_photo = ImageTk.PhotoImage(grid_image)
                self.grid_map_label.configure(image=grid_photo, text="")
                self.grid_map_label.image = grid_photo

            # Update statistics display
            self.update_stats_display()

            # Update GPS display
            self.update_gps_display()

        except Exception as e:
            print(f"Error updating display: {e}")

    def update_gps_display(self):
        """Update GPS coordinates display in video header"""
        if self.geotracker and hasattr(self, 'gps_display_label'):
            current_pos = self.geotracker.current_position
            lat = current_pos['latitude']
            lon = current_pos['longitude']
            alt = current_pos['altitude']

            if lat != 0 or lon != 0:
                gps_text = f"🌍 GPS: {lat:.6f}°, {lon:.6f}° | 🚁 {alt:.1f}m"
            else:
                gps_text = "🌍 GPS: Initializing..."

            self.gps_display_label.config(text=gps_text)

    def open_gps_map(self):
        """Open interactive GPS map in web browser"""
        if not self.geotracker:
            messagebox.showinfo("GPS Map", "Geotracking not available. Please ensure folium and geopy are installed.")
            return

        try:
            # Create interactive map
            map_filename = "current_tracking_map.html"
            self.geotracker.create_web_map(map_filename, include_people=True)

            # Open in web browser
            import webbrowser
            import os
            webbrowser.open(f"file://{os.path.abspath(map_filename)}")

            messagebox.showinfo("GPS Map", f"Interactive map opened in your web browser!\n\nMap file: {map_filename}")

        except Exception as e:
            messagebox.showerror("GPS Map Error", f"Failed to create GPS map: {str(e)}")

    def export_gps_data(self):
        """Export GPS tracking data to JSON file"""
        if not self.geotracker:
            messagebox.showinfo("GPS Export", "No GPS data available to export.")
            return

        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gps_tracking_data_{timestamp}.json"

            self.geotracker.export_geo_data(filename)
            messagebox.showinfo("GPS Export", f"GPS data exported successfully!\n\nFile: {filename}")

        except Exception as e:
            messagebox.showerror("GPS Export Error", f"Failed to export GPS data: {str(e)}")

    def update_stats_display(self):
        """Update the statistics labels"""
        for key, label in self.stat_labels.items():
            value = self.stats.get(key, 0)

            if key == 'avg_fps':
                text = f"{value:.1f}"
            elif key == 'grid_density':
                text = f"{value:.1f}%"
                # Color code density
                if value > 20:
                    label.config(fg='#ff6666')  # Red for high density
                elif value > 10:
                    label.config(fg='#ffff00')  # Yellow for medium density
                else:
                    label.config(fg='#00ff00')  # Green for low density
            elif key in ['drone_lat', 'drone_lon']:
                text = f"{value:.6f}°"
            elif key == 'drone_alt':
                text = f"{value:.1f}m"
            elif key == 'flight_distance':
                if value >= 1000:
                    text = f"{value/1000:.2f}km"
                else:
                    text = f"{value:.1f}m"
            elif key == 'trend':
                text = str(value).title()
                # Color code trends
                if value == 'increasing':
                    label.config(fg='#00ff00')  # Green
                elif value == 'decreasing':
                    label.config(fg='#ff6666')  # Red
                else:
                    label.config(fg='#ffff00')  # Yellow
            else:
                text = str(int(value))

            label.config(text=text)

    def update_status(self, message):
        """Update the status bar message"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def show_final_stats(self):
        """Show final statistics in a popup"""
        if self.stats['total_frames'] == 0:
            return

        stats_text = f"""
Final Tracking Statistics:

Total Frames Processed: {self.stats['total_frames']}
Unique People Identified: {self.stats['unique_people']}
Maximum People in Frame: {self.stats['max_people']}
Average FPS: {self.stats['avg_fps']:.1f}

Track Quality:
- Active Tracks: {self.stats['active_tracks']}
- Confirmed Tracks: {self.stats['confirmed_tracks']}
"""

        if self.detector and self.detector.track_manager:
            total_tracks = len(self.detector.track_manager.track_histories)
            merged_tracks = len(self.detector.track_manager.merged_tracks)
            if total_tracks > 0:
                confirmation_rate = (self.stats['unique_people'] / total_tracks) * 100
                stats_text += f"""
Advanced Statistics:
- Total Tracks Created: {total_tracks}
- Tracks Merged (Duplicates): {merged_tracks}
- Track Confirmation Rate: {confirmation_rate:.1f}%
"""

        messagebox.showinfo("Final Statistics", stats_text)

    def on_closing(self):
        """Handle application closing"""
        if self.is_running:
            self.stop_tracking()

        if self.video_capture:
            self.video_capture.release()

        self.root.destroy()

def main():
    """Main application entry point"""
    root = tk.Tk()
    app = TrackingGUI(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start the GUI
    root.mainloop()

if __name__ == "__main__":
    main()
