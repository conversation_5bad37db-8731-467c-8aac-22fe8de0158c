#!/usr/bin/env python3
"""
Enhanced Person Tracking GUI Application
Real-time video processing with perspective mapping and tracking analytics
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
from collections import deque
import json
import os
from datetime import datetime

# Import our enhanced tracking system
from obj_counter import PersonDetector, FrameComparator
from live_grid_map import LiveGridMap

# Check for geotracking availability
try:
    from geotracking_system import GeoTracker
    GEOTRACKING_AVAILABLE = True
except ImportError:
    GEOTRACKING_AVAILABLE = False

class TrackingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Person Tracking System")
        self.root.geometry("1600x900")
        self.root.configure(bg='#2b2b2b')
        
        # Initialize variables
        self.detector = None
        self.video_capture = None
        self.is_running = False
        self.is_paused = False
        self.current_frame = None
        self.bird_eye_frame = None
        self.grid_map_frame = None
        self.frame_count = 0
        self.fps_counter = deque(maxlen=30)
        self.comparator = FrameComparator(history_size=60)

        # Initialize live grid map
        self.grid_map = LiveGridMap(width=400, height=300, grid_size=20)
        
        # Tracking statistics
        self.stats = {
            'current_people': 0,
            'unique_people': 0,
            'max_people': 0,
            'total_frames': 0,
            'active_tracks': 0,
            'confirmed_tracks': 0,
            'avg_fps': 0.0,
            'trend': 'stable'
        }
        
        # Setup GUI
        self.setup_gui()
        self.setup_styles()
        
        # Initialize detector with default settings
        self.initialize_detector()
        
    def setup_styles(self):
        """Setup custom styles for the GUI"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Stat.TLabel', font=('Arial', 12, 'bold'), background='#3b3b3b', foreground='#00ff00')
        style.configure('Info.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='white')
        style.configure('Control.TButton', font=('Arial', 10, 'bold'))
        
    def setup_gui(self):
        """Setup the main GUI layout"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top control panel
        self.setup_control_panel(main_frame)
        
        # Video display area
        self.setup_video_area(main_frame)
        
        # Statistics panel
        self.setup_stats_panel(main_frame)
        
        # Bottom status bar
        self.setup_status_bar(main_frame)
        
    def setup_control_panel(self, parent):
        """Setup the control panel with buttons and settings"""
        control_frame = tk.Frame(parent, bg='#3b3b3b', relief=tk.RAISED, bd=2)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Title
        title_label = ttk.Label(control_frame, text="Enhanced Person Tracking System", style='Title.TLabel')
        title_label.pack(pady=10)
        
        # Control buttons frame
        buttons_frame = tk.Frame(control_frame, bg='#3b3b3b')
        buttons_frame.pack(pady=5)
        
        # File selection
        ttk.Button(buttons_frame, text="Select Video File", command=self.select_video_file, style='Control.TButton').pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Use Webcam", command=self.use_webcam, style='Control.TButton').pack(side=tk.LEFT, padx=5)
        
        # Control buttons
        self.start_button = ttk.Button(buttons_frame, text="Start", command=self.start_tracking, style='Control.TButton')
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.pause_button = ttk.Button(buttons_frame, text="Pause", command=self.pause_tracking, style='Control.TButton', state=tk.DISABLED)
        self.pause_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(buttons_frame, text="Stop", command=self.stop_tracking, style='Control.TButton', state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Settings frame
        settings_frame = tk.Frame(control_frame, bg='#3b3b3b')
        settings_frame.pack(pady=5)
        
        # Confidence threshold
        tk.Label(settings_frame, text="Confidence:", bg='#3b3b3b', fg='white').pack(side=tk.LEFT, padx=5)
        self.confidence_var = tk.DoubleVar(value=0.6)
        confidence_scale = tk.Scale(settings_frame, from_=0.1, to=1.0, resolution=0.1, orient=tk.HORIZONTAL, 
                                  variable=self.confidence_var, bg='#3b3b3b', fg='white', highlightbackground='#3b3b3b')
        confidence_scale.pack(side=tk.LEFT, padx=5)
        
        # Track duration
        tk.Label(settings_frame, text="Min Track Duration:", bg='#3b3b3b', fg='white').pack(side=tk.LEFT, padx=5)
        self.track_duration_var = tk.IntVar(value=15)
        duration_scale = tk.Scale(settings_frame, from_=5, to=30, orient=tk.HORIZONTAL, 
                                variable=self.track_duration_var, bg='#3b3b3b', fg='white', highlightbackground='#3b3b3b')
        duration_scale.pack(side=tk.LEFT, padx=5)
        
        # Checkboxes
        self.show_perspective_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_frame, text="Show Perspective", variable=self.show_perspective_var, 
                      bg='#3b3b3b', fg='white', selectcolor='#3b3b3b').pack(side=tk.LEFT, padx=5)
        
        self.show_bird_eye_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_frame, text="Bird's Eye View", variable=self.show_bird_eye_var, 
                      bg='#3b3b3b', fg='white', selectcolor='#3b3b3b').pack(side=tk.LEFT, padx=5)
        
    def setup_video_area(self, parent):
        """Setup the video display area"""
        video_frame = tk.Frame(parent, bg='#2b2b2b')
        video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Main video display
        main_video_frame = tk.Frame(video_frame, bg='#1b1b1b', relief=tk.SUNKEN, bd=2)
        main_video_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        tk.Label(main_video_frame, text="Main Video Feed", bg='#1b1b1b', fg='white', font=('Arial', 12, 'bold')).pack(pady=5)
        self.main_video_label = tk.Label(main_video_frame, bg='black')
        self.main_video_label.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Right panel for bird's eye view and grid map
        right_panel = tk.Frame(video_frame, bg='#1b1b1b')
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))

        # Bird's eye view display
        bird_eye_frame = tk.Frame(right_panel, bg='#1b1b1b', relief=tk.SUNKEN, bd=2)
        bird_eye_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        tk.Label(bird_eye_frame, text="🗺️ Bird's Eye View", bg='#1b1b1b', fg='white', font=('Arial', 10, 'bold')).pack(pady=2)
        self.bird_eye_label = tk.Label(bird_eye_frame, bg='black', width=35, height=20)
        self.bird_eye_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Grid map display
        grid_map_frame = tk.Frame(right_panel, bg='#1b1b1b', relief=tk.SUNKEN, bd=2)
        grid_map_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        tk.Label(grid_map_frame, text="🎯 Live Grid Map", bg='#1b1b1b', fg='white', font=('Arial', 10, 'bold')).pack(pady=2)
        self.grid_map_label = tk.Label(grid_map_frame, bg='black', width=35, height=20)
        self.grid_map_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
    def setup_stats_panel(self, parent):
        """Setup the statistics panel"""
        stats_frame = tk.Frame(parent, bg='#3b3b3b', relief=tk.RAISED, bd=2)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Statistics title
        ttk.Label(stats_frame, text="Real-time Statistics", style='Title.TLabel').pack(pady=5)
        
        # Create statistics grid
        stats_grid = tk.Frame(stats_frame, bg='#3b3b3b')
        stats_grid.pack(pady=5)
        
        # Statistics labels
        self.stat_labels = {}
        stats_config = [
            ('Current People', 'current_people', 0, 0),
            ('Unique People', 'unique_people', 0, 1),
            ('Max People', 'max_people', 0, 2),
            ('Active Tracks', 'active_tracks', 1, 0),
            ('Confirmed Tracks', 'confirmed_tracks', 1, 1),
            ('FPS', 'avg_fps', 1, 2),
            ('Total Frames', 'total_frames', 2, 0),
            ('Trend', 'trend', 2, 1),
            ('Grid Density', 'grid_density', 2, 2),
        ]
        
        for label_text, key, row, col in stats_config:
            frame = tk.Frame(stats_grid, bg='#4b4b4b', relief=tk.RAISED, bd=1)
            frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
            
            tk.Label(frame, text=label_text, bg='#4b4b4b', fg='white', font=('Arial', 10)).pack()
            stat_label = tk.Label(frame, text="0", bg='#4b4b4b', fg='#00ff00', font=('Arial', 14, 'bold'))
            stat_label.pack()
            self.stat_labels[key] = stat_label
        
        # Configure grid weights
        for i in range(3):
            stats_grid.columnconfigure(i, weight=1)

    def setup_status_bar(self, parent):
        """Setup the status bar"""
        self.status_frame = tk.Frame(parent, bg='#1b1b1b', relief=tk.SUNKEN, bd=1)
        self.status_frame.pack(fill=tk.X)

        self.status_label = tk.Label(self.status_frame, text="Ready - Select video source to begin",
                                   bg='#1b1b1b', fg='white', font=('Arial', 10))
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_frame, variable=self.progress_var, length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=10, pady=5)

    def initialize_detector(self):
        """Initialize the person detector with default settings"""
        try:
            self.detector = PersonDetector(
                confidence_threshold=self.confidence_var.get(),
                use_tracking=True,
                min_track_duration=self.track_duration_var.get(),
                track_confidence_threshold=0.6,
                use_perspective=True
            )
            self.update_status("Detector initialized successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to initialize detector: {str(e)}")
            self.update_status(f"Error: {str(e)}")

    def select_video_file(self):
        """Select a video file for processing"""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.video_source = file_path
            self.update_status(f"Selected video: {os.path.basename(file_path)}")
            self.start_button.config(state=tk.NORMAL)

    def use_webcam(self):
        """Use webcam as video source"""
        self.video_source = 0
        self.update_status("Webcam selected as video source")
        self.start_button.config(state=tk.NORMAL)

    def start_tracking(self):
        """Start the tracking process"""
        if not hasattr(self, 'video_source'):
            messagebox.showwarning("Warning", "Please select a video source first")
            return

        try:
            # Update detector settings
            self.detector.confidence_threshold = self.confidence_var.get()
            if self.detector.track_manager:
                self.detector.track_manager.min_track_duration = self.track_duration_var.get()

            # Open video capture
            self.video_capture = cv2.VideoCapture(self.video_source)
            if not self.video_capture.isOpened():
                raise Exception("Failed to open video source")

            # Get video properties
            if isinstance(self.video_source, str):
                total_frames = int(self.video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = self.video_capture.get(cv2.CAP_PROP_FPS)
                self.progress_bar.config(maximum=total_frames)
                self.update_status(f"Video loaded: {total_frames} frames, {fps:.1f} FPS")
            else:
                self.progress_bar.config(maximum=100)
                self.update_status("Webcam started")

            # Start processing thread
            self.is_running = True
            self.is_paused = False
            self.frame_count = 0

            # Update button states
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)

            # Start processing in separate thread
            self.processing_thread = threading.Thread(target=self.process_video, daemon=True)
            self.processing_thread.start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start tracking: {str(e)}")
            self.update_status(f"Error: {str(e)}")

    def pause_tracking(self):
        """Pause/resume tracking"""
        self.is_paused = not self.is_paused
        if self.is_paused:
            self.pause_button.config(text="Resume")
            self.update_status("Tracking paused")
        else:
            self.pause_button.config(text="Pause")
            self.update_status("Tracking resumed")

    def stop_tracking(self):
        """Stop the tracking process"""
        self.is_running = False

        if self.video_capture:
            self.video_capture.release()
            self.video_capture = None

        # Update button states
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="Pause")
        self.stop_button.config(state=tk.DISABLED)

        self.update_status("Tracking stopped")

        # Show final statistics
        self.show_final_stats()

    def process_video(self):
        """Main video processing loop"""
        while self.is_running:
            if self.is_paused:
                time.sleep(0.1)
                continue

            ret, frame = self.video_capture.read()
            if not ret:
                if isinstance(self.video_source, str):  # Video file ended
                    self.root.after(0, self.stop_tracking)
                    break
                else:  # Webcam error
                    continue

            frame_start_time = time.time()

            try:
                # Process frame
                self.current_frame = frame.copy()
                self.frame_count += 1

                # Detect people
                detections, frame_people_count, inference_time = self.detector.detect_people_in_frame(frame)

                # Track people
                tracks = self.detector.track_people(frame, detections, self.frame_count) if self.detector.use_tracking else []

                # Get detection details for comparator
                detection_details, confidence_scores = self.detector.get_detection_details(detections)

                # Calculate unique count
                unique_count = 0
                if self.detector.use_tracking and self.detector.track_manager:
                    unique_count = self.detector.track_manager.get_confirmed_unique_count()

                # Prepare bbox data
                bbox_data = []
                if tracks:
                    bbox_data = [track['bbox'] for track in tracks]
                elif detection_details:
                    bbox_data = [det['bbox'] for det in detection_details]

                # Update comparator with correct arguments - add_frame_data returns the analysis
                comparison_analysis = self.comparator.add_frame_data(
                    self.frame_count,
                    frame_people_count,
                    unique_count,
                    confidence_scores,
                    bbox_data,
                    inference_time
                )

                # Update statistics
                self.update_statistics(frame_people_count, tracks, comparison_analysis, inference_time, unique_count)

                # Draw annotations
                annotated_frame = self.detector.draw_detections_on_frame(
                    frame, detections, tracks, self.show_perspective_var.get()
                )

                # Get bird's eye view if enabled
                bird_eye_frame = None
                if self.show_bird_eye_var.get():
                    bird_eye_frame = self.detector.get_bird_eye_view(frame, tracks)

                # Update grid map with current people positions
                grid_map_frame = None
                if tracks:
                    # Get frame dimensions
                    frame_height, frame_width = frame.shape[:2]
                    grid_map_frame = self.grid_map.update_people_positions(tracks, frame_width, frame_height)

                # Update GUI in main thread
                self.root.after(0, self.update_display, annotated_frame, bird_eye_frame, grid_map_frame)

                # Calculate FPS
                frame_time = time.time() - frame_start_time
                self.fps_counter.append(1.0 / frame_time if frame_time > 0 else 0)

                # Update progress
                if isinstance(self.video_source, str):
                    self.progress_var.set(self.frame_count)

                # Control frame rate for webcam
                if isinstance(self.video_source, int):
                    time.sleep(0.033)  # ~30 FPS for webcam

            except Exception as e:
                print(f"Error processing frame {self.frame_count}: {e}")
                continue

    def update_statistics(self, frame_people_count, tracks, comparison_analysis, inference_time, unique_count=0):
        """Update tracking statistics"""
        # Basic stats
        self.stats['current_people'] = frame_people_count
        self.stats['total_frames'] = self.frame_count
        self.stats['trend'] = comparison_analysis.get('trend', 'stable')
        self.stats['inference_time'] = inference_time
        self.stats['unique_people'] = unique_count

        # Track stats
        if tracks:
            self.stats['active_tracks'] = len(tracks)
            if self.detector.track_manager:
                confirmed = len([t for t in tracks if self.detector.track_manager.should_confirm_track(t['track_id'])])
                self.stats['confirmed_tracks'] = confirmed

        # Max people
        if frame_people_count > self.stats['max_people']:
            self.stats['max_people'] = frame_people_count

        # FPS calculation
        if self.fps_counter:
            self.stats['avg_fps'] = sum(self.fps_counter) / len(self.fps_counter)

        # Grid map statistics
        if self.grid_map:
            grid_analytics = self.grid_map.get_grid_analytics()
            self.stats['grid_density'] = grid_analytics.get('density_percentage', 0)

    def update_display(self, main_frame, bird_eye_frame, grid_map_frame=None):
        """Update the video displays and statistics"""
        try:
            # Update main video display
            if main_frame is not None:
                # Resize frame to fit display
                display_frame = cv2.resize(main_frame, (800, 600))
                display_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)
                image = Image.fromarray(display_frame)
                photo = ImageTk.PhotoImage(image)
                self.main_video_label.configure(image=photo)
                self.main_video_label.image = photo

            # Update bird's eye view display
            if bird_eye_frame is not None and self.show_bird_eye_var.get():
                # Resize bird's eye view
                bird_display = cv2.resize(bird_eye_frame, (300, 200))
                bird_display = cv2.cvtColor(bird_display, cv2.COLOR_BGR2RGB)
                bird_image = Image.fromarray(bird_display)
                bird_photo = ImageTk.PhotoImage(bird_image)
                self.bird_eye_label.configure(image=bird_photo)
                self.bird_eye_label.image = bird_photo

            # Update grid map display
            if grid_map_frame is not None:
                # Resize grid map
                grid_display = cv2.resize(grid_map_frame, (300, 200))
                grid_display = cv2.cvtColor(grid_display, cv2.COLOR_BGR2RGB)
                grid_image = Image.fromarray(grid_display)
                grid_photo = ImageTk.PhotoImage(grid_image)
                self.grid_map_label.configure(image=grid_photo)
                self.grid_map_label.image = grid_photo

            # Update statistics display
            self.update_stats_display()

        except Exception as e:
            print(f"Error updating display: {e}")

    def update_stats_display(self):
        """Update the statistics labels"""
        for key, label in self.stat_labels.items():
            value = self.stats.get(key, 0)

            if key == 'avg_fps':
                text = f"{value:.1f}"
            elif key == 'grid_density':
                text = f"{value:.1f}%"
                # Color code density
                if value > 20:
                    label.config(fg='#ff6666')  # Red for high density
                elif value > 10:
                    label.config(fg='#ffff00')  # Yellow for medium density
                else:
                    label.config(fg='#00ff00')  # Green for low density
            elif key == 'trend':
                text = str(value).title()
                # Color code trends
                if value == 'increasing':
                    label.config(fg='#00ff00')  # Green
                elif value == 'decreasing':
                    label.config(fg='#ff6666')  # Red
                else:
                    label.config(fg='#ffff00')  # Yellow
            else:
                text = str(int(value))

            label.config(text=text)

    def update_status(self, message):
        """Update the status bar message"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def show_final_stats(self):
        """Show final statistics in a popup"""
        if self.stats['total_frames'] == 0:
            return

        stats_text = f"""
Final Tracking Statistics:

Total Frames Processed: {self.stats['total_frames']}
Unique People Identified: {self.stats['unique_people']}
Maximum People in Frame: {self.stats['max_people']}
Average FPS: {self.stats['avg_fps']:.1f}

Track Quality:
- Active Tracks: {self.stats['active_tracks']}
- Confirmed Tracks: {self.stats['confirmed_tracks']}
"""

        if self.detector and self.detector.track_manager:
            total_tracks = len(self.detector.track_manager.track_histories)
            merged_tracks = len(self.detector.track_manager.merged_tracks)
            if total_tracks > 0:
                confirmation_rate = (self.stats['unique_people'] / total_tracks) * 100
                stats_text += f"""
Advanced Statistics:
- Total Tracks Created: {total_tracks}
- Tracks Merged (Duplicates): {merged_tracks}
- Track Confirmation Rate: {confirmation_rate:.1f}%
"""

        messagebox.showinfo("Final Statistics", stats_text)

    def on_closing(self):
        """Handle application closing"""
        if self.is_running:
            self.stop_tracking()

        if self.video_capture:
            self.video_capture.release()

        self.root.destroy()

def main():
    """Main application entry point"""
    root = tk.Tk()
    app = TrackingGUI(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start the GUI
    root.mainloop()

if __name__ == "__main__":
    main()
