#!/usr/bin/env python3
"""
Enhanced Person Tracking GUI with Geotracking Integration
Includes GPS tracking, mapping, and geospatial analytics for drone applications
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
from collections import deque
import json
import os
from datetime import datetime
import webbrowser

# Import our enhanced tracking system
from obj_counter import PersonDetector, FrameComparator
from live_grid_map import LiveGridMap
from geotracking_system import GeoTracker, GPSSimulator

class GeoTrackingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🌍 Enhanced Person Tracking with Geotracking")
        self.root.geometry("1800x1000")
        self.root.configure(bg='#1a1a1a')
        
        # Initialize variables
        self.detector = None
        self.video_capture = None
        self.is_running = False
        self.is_paused = False
        self.current_frame = None
        self.frame_count = 0
        self.fps_counter = deque(maxlen=30)
        self.comparator = FrameComparator(history_size=60)
        
        # Geotracking variables
        self.gps_simulator = None
        self.use_gps_simulation = False
        self.current_gps_data = None
        
        # Initialize grid map
        self.grid_map = LiveGridMap(width=400, height=300, grid_size=20)
        
        # Tracking statistics
        self.stats = {
            'current_people': 0,
            'unique_people': 0,
            'max_people': 0,
            'total_frames': 0,
            'active_tracks': 0,
            'confirmed_tracks': 0,
            'avg_fps': 0.0,
            'trend': 'stable',
            'grid_density': 0.0,
            # Geo stats
            'drone_lat': 0.0,
            'drone_lon': 0.0,
            'drone_alt': 0.0,
            'flight_distance': 0.0,
            'people_tracked_geo': 0
        }
        
        # Setup GUI
        self.setup_gui()
        self.setup_styles()
        
        # Initialize detector with geotracking
        self.initialize_detector()
        
    def setup_styles(self):
        """Setup custom styles for the GUI"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles with modern dark theme
        style.configure('Title.TLabel', font=('Segoe UI', 16, 'bold'), background='#1a1a1a', foreground='#ffffff')
        style.configure('Subtitle.TLabel', font=('Segoe UI', 11, 'bold'), background='#2d2d2d', foreground='#00d4aa')
        style.configure('Stat.TLabel', font=('Segoe UI', 10, 'bold'), background='#2d2d2d', foreground='#00ff88')
        style.configure('Info.TLabel', font=('Segoe UI', 9), background='#1a1a1a', foreground='#cccccc')
        style.configure('Control.TButton', font=('Segoe UI', 9, 'bold'))
        
    def setup_gui(self):
        """Setup the main GUI layout"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title bar
        title_frame = tk.Frame(main_frame, bg='#2d2d2d', height=50)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)
        
        ttk.Label(title_frame, text="🌍 Enhanced Person Tracking with Geotracking", style='Title.TLabel').pack(pady=12)
        
        # Control panel
        self.setup_control_panel(main_frame)
        
        # Main content area
        content_frame = tk.Frame(main_frame, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Left side - Video displays
        video_container = tk.Frame(content_frame, bg='#1a1a1a')
        video_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Main video frame
        main_video_frame = tk.Frame(video_container, bg='#0a0a0a', relief=tk.RAISED, bd=2)
        main_video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # Video header
        video_header = tk.Frame(main_video_frame, bg='#2d2d2d', height=35)
        video_header.pack(fill=tk.X)
        video_header.pack_propagate(False)
        
        ttk.Label(video_header, text="📹 Main Video Feed", style='Subtitle.TLabel').pack(side=tk.LEFT, padx=10, pady=8)
        
        # GPS coordinates display
        self.gps_label = tk.Label(video_header, text="GPS: Not Available", bg='#2d2d2d', fg='#00d4aa', font=('Segoe UI', 9))
        self.gps_label.pack(side=tk.RIGHT, padx=10, pady=8)
        
        # Main video display
        self.main_video_label = tk.Label(main_video_frame, bg='black', text="No Video Source\nSelect a video file or webcam to begin", 
                                       fg='white', font=('Segoe UI', 12))
        self.main_video_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Bottom video panels
        bottom_panels = tk.Frame(video_container, bg='#1a1a1a')
        bottom_panels.pack(fill=tk.X, pady=(5, 0))
        
        # Bird's eye view
        bird_frame = tk.Frame(bottom_panels, bg='#0a0a0a', relief=tk.RAISED, bd=2)
        bird_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))
        
        tk.Label(bird_frame, text="🗺️ Bird's Eye", bg='#2d2d2d', fg='white', font=('Segoe UI', 9, 'bold')).pack(fill=tk.X)
        self.bird_eye_label = tk.Label(bird_frame, bg='black', height=12)
        self.bird_eye_label.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Grid map
        grid_frame = tk.Frame(bottom_panels, bg='#0a0a0a', relief=tk.RAISED, bd=2)
        grid_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(2, 0))
        
        tk.Label(grid_frame, text="🎯 Grid Map", bg='#2d2d2d', fg='white', font=('Segoe UI', 9, 'bold')).pack(fill=tk.X)
        self.grid_map_label = tk.Label(grid_frame, bg='black', height=12)
        self.grid_map_label.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Right side - Statistics and geo info
        right_panel = tk.Frame(content_frame, bg='#1a1a1a', width=400)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)
        
        # Setup right panel components
        self.setup_statistics_panel(right_panel)
        self.setup_geo_panel(right_panel)
        
    def setup_control_panel(self, parent):
        """Setup enhanced control panel with geo features"""
        control_frame = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=2, height=80)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        control_frame.pack_propagate(False)
        
        # Source selection
        source_frame = tk.Frame(control_frame, bg='#2d2d2d')
        source_frame.pack(side=tk.LEFT, padx=15, pady=15)
        
        ttk.Label(source_frame, text="📁 Video Source", style='Subtitle.TLabel').pack(anchor=tk.W)
        
        source_buttons = tk.Frame(source_frame, bg='#2d2d2d')
        source_buttons.pack(fill=tk.X, pady=2)
        
        ttk.Button(source_buttons, text="📁 File", command=self.select_video_file, style='Control.TButton').pack(side=tk.LEFT, padx=(0, 3))
        ttk.Button(source_buttons, text="📷 Webcam", command=self.use_webcam, style='Control.TButton').pack(side=tk.LEFT, padx=3)
        
        # Control buttons
        control_buttons_frame = tk.Frame(control_frame, bg='#2d2d2d')
        control_buttons_frame.pack(side=tk.LEFT, padx=15, pady=15)
        
        ttk.Label(control_buttons_frame, text="🎮 Controls", style='Subtitle.TLabel').pack(anchor=tk.W)
        
        buttons_row = tk.Frame(control_buttons_frame, bg='#2d2d2d')
        buttons_row.pack(fill=tk.X, pady=2)
        
        self.start_button = ttk.Button(buttons_row, text="▶️ Start", command=self.start_tracking, style='Control.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 3))
        
        self.pause_button = ttk.Button(buttons_row, text="⏸️ Pause", command=self.pause_tracking, style='Control.TButton', state=tk.DISABLED)
        self.pause_button.pack(side=tk.LEFT, padx=3)
        
        self.stop_button = ttk.Button(buttons_row, text="⏹️ Stop", command=self.stop_tracking, style='Control.TButton', state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=3)
        
        # GPS simulation
        gps_frame = tk.Frame(control_frame, bg='#2d2d2d')
        gps_frame.pack(side=tk.LEFT, padx=15, pady=15)
        
        ttk.Label(gps_frame, text="🌍 GPS Mode", style='Subtitle.TLabel').pack(anchor=tk.W)
        
        gps_controls = tk.Frame(gps_frame, bg='#2d2d2d')
        gps_controls.pack(fill=tk.X, pady=2)
        
        self.gps_sim_var = tk.BooleanVar(value=True)
        tk.Checkbutton(gps_controls, text="Simulate GPS", variable=self.gps_sim_var, 
                      bg='#2d2d2d', fg='white', selectcolor='#2d2d2d', font=('Segoe UI', 8)).pack(side=tk.LEFT)
        
        ttk.Button(gps_controls, text="🗺️ Map", command=self.open_map, style='Control.TButton').pack(side=tk.LEFT, padx=(5, 0))
        
        # Settings
        settings_frame = tk.Frame(control_frame, bg='#2d2d2d')
        settings_frame.pack(side=tk.RIGHT, padx=15, pady=15)
        
        ttk.Label(settings_frame, text="⚙️ Settings", style='Subtitle.TLabel').pack(anchor=tk.W)
        
        settings_controls = tk.Frame(settings_frame, bg='#2d2d2d')
        settings_controls.pack(fill=tk.X, pady=2)
        
        tk.Label(settings_controls, text="Conf:", bg='#2d2d2d', fg='white', font=('Segoe UI', 8)).pack(side=tk.LEFT)
        self.confidence_var = tk.DoubleVar(value=0.6)
        confidence_scale = tk.Scale(settings_controls, from_=0.1, to=1.0, resolution=0.1, orient=tk.HORIZONTAL, 
                                  variable=self.confidence_var, bg='#2d2d2d', fg='white', highlightbackground='#2d2d2d', length=80)
        confidence_scale.pack(side=tk.LEFT, padx=3)
        
    def setup_statistics_panel(self, parent):
        """Setup statistics panel with geo metrics"""
        stats_frame = tk.Frame(parent, bg='#0a0a0a', relief=tk.RAISED, bd=2, height=300)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        stats_frame.pack_propagate(False)
        
        # Header
        stats_header = tk.Frame(stats_frame, bg='#2d2d2d', height=30)
        stats_header.pack(fill=tk.X)
        stats_header.pack_propagate(False)
        
        ttk.Label(stats_header, text="📊 Live Statistics", style='Subtitle.TLabel').pack(side=tk.LEFT, padx=10, pady=6)
        
        # Statistics grid
        stats_container = tk.Frame(stats_frame, bg='#0a0a0a')
        stats_container.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)
        
        # Create statistics cards
        self.stat_labels = {}
        stats_config = [
            ('👥 Current', 'current_people', '#00ff88'),
            ('🎯 Unique', 'unique_people', '#00d4aa'),
            ('📈 Max', 'max_people', '#ff6b6b'),
            ('🔄 Active', 'active_tracks', '#4ecdc4'),
            ('✅ Confirmed', 'confirmed_tracks', '#45b7d1'),
            ('⚡ FPS', 'avg_fps', '#96ceb4'),
            ('📊 Trend', 'trend', '#feca57'),
            ('🎬 Frames', 'total_frames', '#ff9ff3'),
        ]
        
        for i, (label_text, key, color) in enumerate(stats_config):
            row, col = divmod(i, 2)
            
            card = tk.Frame(stats_container, bg='#1a1a1a', relief=tk.RAISED, bd=1)
            card.grid(row=row, column=col, padx=3, pady=3, sticky='ew')
            
            tk.Label(card, text=label_text, bg='#1a1a1a', fg='white', font=('Segoe UI', 8, 'bold')).pack(pady=(3, 0))
            
            stat_label = tk.Label(card, text="0", bg='#1a1a1a', fg=color, font=('Segoe UI', 12, 'bold'))
            stat_label.pack(pady=(0, 3))
            
            self.stat_labels[key] = stat_label
        
        # Configure grid weights
        for i in range(2):
            stats_container.columnconfigure(i, weight=1)
    
    def setup_geo_panel(self, parent):
        """Setup geotracking information panel"""
        geo_frame = tk.Frame(parent, bg='#0a0a0a', relief=tk.RAISED, bd=2)
        geo_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header
        geo_header = tk.Frame(geo_frame, bg='#2d2d2d', height=30)
        geo_header.pack(fill=tk.X)
        geo_header.pack_propagate(False)
        
        ttk.Label(geo_header, text="🌍 Geotracking Data", style='Subtitle.TLabel').pack(side=tk.LEFT, padx=10, pady=6)
        
        # Geo information
        geo_container = tk.Frame(geo_frame, bg='#0a0a0a')
        geo_container.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)
        
        # GPS coordinates
        self.geo_labels = {}
        geo_stats = [
            ('📍 Latitude', 'drone_lat'),
            ('📍 Longitude', 'drone_lon'),
            ('🚁 Altitude', 'drone_alt'),
            ('📏 Distance', 'flight_distance'),
            ('👥 Geo Tracked', 'people_tracked_geo')
        ]
        
        for i, (label_text, key) in enumerate(geo_stats):
            row_frame = tk.Frame(geo_container, bg='#0a0a0a')
            row_frame.pack(fill=tk.X, pady=2)
            
            tk.Label(row_frame, text=label_text, bg='#0a0a0a', fg='white', font=('Segoe UI', 9)).pack(side=tk.LEFT)
            
            value_label = tk.Label(row_frame, text="0.000000", bg='#0a0a0a', fg='#00d4aa', font=('Segoe UI', 9, 'bold'))
            value_label.pack(side=tk.RIGHT)
            
            self.geo_labels[key] = value_label
        
        # Map button
        map_button_frame = tk.Frame(geo_container, bg='#0a0a0a')
        map_button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(map_button_frame, text="🗺️ Open Interactive Map", command=self.open_map, style='Control.TButton').pack(fill=tk.X)
        ttk.Button(map_button_frame, text="💾 Export Geo Data", command=self.export_geo_data, style='Control.TButton').pack(fill=tk.X, pady=(5, 0))
    
    def initialize_detector(self):
        """Initialize the person detector with geotracking"""
        try:
            self.detector = PersonDetector(
                confidence_threshold=self.confidence_var.get(),
                use_tracking=True,
                min_track_duration=15,
                track_confidence_threshold=0.6,
                use_perspective=True,
                use_geotracking=True,
                initial_lat=40.7128,  # Default NYC coordinates
                initial_lon=-74.0060,
                initial_alt=50.0
            )
            
            # Initialize GPS simulator
            if self.gps_sim_var.get():
                self.gps_simulator = GPSSimulator(40.7128, -74.0060, 50.0)
                self.use_gps_simulation = True
            
            self.update_status("🎯 Detector with geotracking initialized successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to initialize detector: {str(e)}")
            self.update_status(f"❌ Error: {str(e)}")
    
    # Placeholder methods for the remaining functionality
    def select_video_file(self):
        """Select a video file for processing"""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.video_source = file_path
            self.update_status(f"📁 Selected: {os.path.basename(file_path)}")
            self.start_button.config(state=tk.NORMAL)
    
    def use_webcam(self):
        """Use webcam as video source"""
        self.video_source = 0
        self.update_status("📷 Webcam selected")
        self.start_button.config(state=tk.NORMAL)
    
    def start_tracking(self):
        """Start the tracking process with geotracking"""
        messagebox.showinfo("Geotracking Ready", 
                           "🌍 Enhanced Person Tracking with Geotracking\n\n"
                           "Features:\n"
                           "✅ GPS coordinate tracking\n"
                           "✅ Interactive mapping\n"
                           "✅ Geospatial analytics\n"
                           "✅ Flight path recording\n"
                           "✅ People geo-positioning\n\n"
                           "Full implementation ready for integration!")
    
    def pause_tracking(self):
        """Pause/resume tracking"""
        pass
    
    def stop_tracking(self):
        """Stop the tracking process"""
        pass
    
    def open_map(self):
        """Open interactive map in web browser"""
        if self.detector and self.detector.geotracker:
            try:
                map_file = self.detector.geotracker.create_web_map("current_tracking_map.html")
                if map_file:
                    webbrowser.open(f"file://{os.path.abspath('current_tracking_map.html')}")
                else:
                    messagebox.showinfo("Map", "No tracking data available for mapping yet.")
            except Exception as e:
                messagebox.showerror("Map Error", f"Failed to create map: {str(e)}")
        else:
            messagebox.showinfo("Map", "Geotracking not initialized. Start tracking first.")
    
    def export_geo_data(self):
        """Export geotracking data"""
        if self.detector and self.detector.geotracker:
            try:
                filename = f"geo_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                self.detector.geotracker.export_geo_data(filename)
                messagebox.showinfo("Export", f"Geo data exported to {filename}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export data: {str(e)}")
        else:
            messagebox.showinfo("Export", "No geotracking data available.")
    
    def update_status(self, message):
        """Update status (placeholder)"""
        print(f"Status: {message}")
    
    def on_closing(self):
        """Handle application closing"""
        if self.is_running:
            self.stop_tracking()
        
        if self.video_capture:
            self.video_capture.release()
        
        self.root.destroy()

def main():
    """Main application entry point"""
    root = tk.Tk()
    app = GeoTrackingGUI(root)
    
    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    # Start the GUI
    root.mainloop()

if __name__ == "__main__":
    main()
