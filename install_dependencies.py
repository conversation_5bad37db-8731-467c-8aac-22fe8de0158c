#!/usr/bin/env python3
"""
Enhanced Person Tracking System - Dependency Installer
Automatically installs all required and optional dependencies
"""

import subprocess
import sys
import importlib
import tkinter as tk
from tkinter import ttk, messagebox
import threading

class DependencyInstaller:
    def __init__(self, root):
        self.root = root
        self.root.title("🔧 Enhanced Person Tracking - Dependency Installer")
        self.root.geometry("700x500")
        self.root.configure(bg='#1a1a1a')
        
        self.required_packages = [
            ('opencv-python', 'cv2', 'Computer vision and video processing'),
            ('ultralytics', 'ultralytics', 'YOLOv8 object detection'),
            ('numpy', 'numpy', 'Numerical computations'),
            ('pillow', 'PIL', 'Image processing'),
            ('matplotlib', 'matplotlib', 'Data visualization and plotting'),
            ('deep-sort-realtime', 'deep_sort_realtime', 'Multi-object tracking')
        ]
        
        self.optional_packages = [
            ('folium', 'folium', 'Interactive mapping for geotracking'),
            ('geopy', 'geopy', 'GPS coordinate calculations'),
            ('requests', 'requests', 'HTTP requests for map tiles'),
            ('branca', 'branca', 'Map visualization components')
        ]
        
        self.setup_gui()
        self.check_current_status()
    
    def setup_gui(self):
        """Setup the installer GUI"""
        # Title
        title_frame = tk.Frame(self.root, bg='#2d2d2d', height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame, text="🔧 Dependency Installer", 
                bg='#2d2d2d', fg='white', font=('Segoe UI', 16, 'bold')).pack(pady=15)
        
        # Main content
        content_frame = tk.Frame(self.root, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Description
        desc_text = "Install required and optional dependencies for the Enhanced Person Tracking System"
        tk.Label(content_frame, text=desc_text, bg='#1a1a1a', fg='#cccccc', 
                font=('Segoe UI', 11)).pack(pady=(0, 20))
        
        # Required packages section
        req_frame = tk.LabelFrame(content_frame, text="Required Packages", 
                                 bg='#2d2d2d', fg='white', font=('Segoe UI', 12, 'bold'))
        req_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.req_tree = ttk.Treeview(req_frame, columns=('Status', 'Description'), show='tree headings', height=6)
        self.req_tree.heading('#0', text='Package')
        self.req_tree.heading('Status', text='Status')
        self.req_tree.heading('Description', text='Description')
        self.req_tree.column('#0', width=150)
        self.req_tree.column('Status', width=100)
        self.req_tree.column('Description', width=300)
        self.req_tree.pack(fill=tk.X, padx=10, pady=10)
        
        # Optional packages section
        opt_frame = tk.LabelFrame(content_frame, text="Optional Packages (for Geotracking)", 
                                 bg='#2d2d2d', fg='white', font=('Segoe UI', 12, 'bold'))
        opt_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.opt_tree = ttk.Treeview(opt_frame, columns=('Status', 'Description'), show='tree headings', height=4)
        self.opt_tree.heading('#0', text='Package')
        self.opt_tree.heading('Status', text='Status')
        self.opt_tree.heading('Description', text='Description')
        self.opt_tree.column('#0', width=150)
        self.opt_tree.column('Status', width=100)
        self.opt_tree.column('Description', width=300)
        self.opt_tree.pack(fill=tk.X, padx=10, pady=10)
        
        # Buttons
        button_frame = tk.Frame(content_frame, bg='#1a1a1a')
        button_frame.pack(fill=tk.X, pady=20)
        
        tk.Button(button_frame, text="🔧 Install Required", command=self.install_required,
                 bg='#00d4aa', fg='black', font=('Segoe UI', 11, 'bold'),
                 padx=20, pady=8).pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Button(button_frame, text="🌍 Install Geotracking", command=self.install_optional,
                 bg='#4ecdc4', fg='black', font=('Segoe UI', 11, 'bold'),
                 padx=20, pady=8).pack(side=tk.LEFT, padx=10)
        
        tk.Button(button_frame, text="📦 Install All", command=self.install_all,
                 bg='#45b7d1', fg='white', font=('Segoe UI', 11, 'bold'),
                 padx=20, pady=8).pack(side=tk.LEFT, padx=10)
        
        tk.Button(button_frame, text="🔄 Refresh", command=self.check_current_status,
                 bg='#96ceb4', fg='black', font=('Segoe UI', 11, 'bold'),
                 padx=20, pady=8).pack(side=tk.RIGHT)
        
        # Progress bar
        self.progress_frame = tk.Frame(content_frame, bg='#1a1a1a')
        self.progress_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.progress_label = tk.Label(self.progress_frame, text="Ready to install dependencies", 
                                     bg='#1a1a1a', fg='white', font=('Segoe UI', 10))
        self.progress_label.pack()
        
        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
    
    def check_package_status(self, package_name, import_name):
        """Check if a package is installed"""
        try:
            importlib.import_module(import_name)
            return "✅ Installed"
        except ImportError:
            return "❌ Missing"
    
    def check_current_status(self):
        """Check current installation status of all packages"""
        # Clear existing items
        for item in self.req_tree.get_children():
            self.req_tree.delete(item)
        for item in self.opt_tree.get_children():
            self.opt_tree.delete(item)
        
        # Check required packages
        for package_name, import_name, description in self.required_packages:
            status = self.check_package_status(package_name, import_name)
            self.req_tree.insert('', 'end', text=package_name, values=(status, description))
        
        # Check optional packages
        for package_name, import_name, description in self.optional_packages:
            status = self.check_package_status(package_name, import_name)
            self.opt_tree.insert('', 'end', text=package_name, values=(status, description))
        
        self.update_progress("Status check completed")
    
    def install_packages(self, packages, package_type="packages"):
        """Install a list of packages"""
        def install_worker():
            self.progress_bar.start()
            total_packages = len(packages)
            
            for i, (package_name, import_name, description) in enumerate(packages):
                self.update_progress(f"Installing {package_name} ({i+1}/{total_packages})...")
                
                try:
                    # Check if already installed
                    importlib.import_module(import_name)
                    self.update_progress(f"{package_name} already installed")
                    continue
                except ImportError:
                    pass
                
                # Install package
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
                    self.update_progress(f"✅ {package_name} installed successfully")
                except subprocess.CalledProcessError as e:
                    self.update_progress(f"❌ Failed to install {package_name}: {e}")
            
            self.progress_bar.stop()
            self.update_progress(f"✅ {package_type} installation completed")
            self.check_current_status()
            
            # Show completion message
            self.root.after(0, lambda: messagebox.showinfo("Installation Complete", 
                                                          f"{package_type} installation finished!\n\n"
                                                          "You can now use the Enhanced Person Tracking System."))
        
        # Run installation in separate thread
        thread = threading.Thread(target=install_worker, daemon=True)
        thread.start()
    
    def install_required(self):
        """Install required packages"""
        if messagebox.askyesno("Install Required", 
                              "Install required packages for basic person tracking?\n\n"
                              "This will install:\n" + 
                              "\n".join([f"• {pkg[0]}" for pkg in self.required_packages])):
            self.install_packages(self.required_packages, "Required packages")
    
    def install_optional(self):
        """Install optional packages for geotracking"""
        if messagebox.askyesno("Install Geotracking", 
                              "Install optional packages for geotracking features?\n\n"
                              "This will install:\n" + 
                              "\n".join([f"• {pkg[0]}" for pkg in self.optional_packages])):
            self.install_packages(self.optional_packages, "Geotracking packages")
    
    def install_all(self):
        """Install all packages"""
        if messagebox.askyesno("Install All", 
                              "Install all packages (required + optional)?\n\n"
                              "This will install all dependencies for the complete system."):
            all_packages = self.required_packages + self.optional_packages
            self.install_packages(all_packages, "All packages")
    
    def update_progress(self, message):
        """Update progress label"""
        self.progress_label.config(text=message)
        self.root.update_idletasks()

def main():
    """Main installer entry point"""
    root = tk.Tk()
    app = DependencyInstaller(root)
    root.mainloop()

if __name__ == "__main__":
    main()
