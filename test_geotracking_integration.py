#!/usr/bin/env python3
"""
Test script to demonstrate geotracking integration in the GUI
"""

import tkinter as tk
from tkinter import messagebox
import time
import threading

def test_geotracking_features():
    """Test geotracking features integration"""
    print("🌍 Testing Geotracking Integration")
    print("=" * 50)
    
    try:
        # Test geotracking system import
        from geotracking_system import GeoTracker, GPSSimulator
        print("✅ Geotracking system imported successfully")
        
        # Test GPS simulator
        gps_sim = GPSSimulator(40.7128, -74.0060, 50.0)
        print("✅ GPS simulator initialized")
        
        # Test geotracker
        tracker = GeoTracker(40.7128, -74.0060, 50.0)
        print("✅ GeoTracker initialized")
        
        # Test GPS position updates
        for i in range(5):
            gps_data = gps_sim.get_next_position()
            tracker.update_drone_position(
                gps_data['latitude'], 
                gps_data['longitude'], 
                gps_data['altitude'],
                gps_data['accuracy'],
                gps_data['heading'],
                gps_data['speed']
            )
            
            # Test people tracking
            tracker.track_person_geo(i, 320 + i*10, 240 + i*10, 640, 480, 0.8)
            
            print(f"  Frame {i+1}: GPS({gps_data['latitude']:.6f}, {gps_data['longitude']:.6f}) Alt: {gps_data['altitude']:.1f}m")
        
        print("✅ GPS position updates working")
        
        # Test statistics
        stats = tracker.get_flight_statistics()
        print("✅ Flight statistics generated:")
        print(f"  - Distance flown: {stats['total_distance_flown']:.2f}m")
        print(f"  - People tracked: {stats['people_tracked']}")
        print(f"  - Current position: {stats['current_position']['latitude']:.6f}, {stats['current_position']['longitude']:.6f}")
        
        # Test data export
        tracker.export_geo_data("test_geo_export.json")
        print("✅ GPS data export working")
        
        # Test map creation (if folium available)
        try:
            import folium
            tracker.create_web_map("test_map.html")
            print("✅ Interactive map creation working")
        except ImportError:
            print("⚠️ Folium not available - map creation skipped")
        
        return True
        
    except Exception as e:
        print(f"❌ Geotracking test failed: {e}")
        return False

def test_gui_integration():
    """Test GUI integration with geotracking"""
    print("\n🎮 Testing GUI Integration")
    print("=" * 50)
    
    try:
        # Test GUI import
        from tracking_gui import TrackingGUI
        print("✅ GUI module imported successfully")
        
        # Create test window (hidden)
        root = tk.Tk()
        root.withdraw()
        
        # Test GUI initialization
        app = TrackingGUI(root)
        print("✅ GUI initialized successfully")
        
        # Check geotracking integration
        if hasattr(app, 'geotracker') and app.geotracker:
            print("✅ Geotracker integrated in GUI")
            
            # Test GPS simulation
            if hasattr(app, 'gps_simulator') and app.gps_simulator:
                print("✅ GPS simulator available in GUI")
            else:
                print("⚠️ GPS simulator not found in GUI")
            
            # Test GPS controls
            if hasattr(app, 'enable_gps_var'):
                print("✅ GPS controls available in GUI")
            else:
                print("⚠️ GPS controls not found in GUI")
            
            # Test GPS display
            if hasattr(app, 'gps_display_label'):
                print("✅ GPS display label available in GUI")
            else:
                print("⚠️ GPS display label not found in GUI")
                
        else:
            print("⚠️ Geotracker not integrated in GUI (may need folium/geopy)")
        
        # Test statistics integration
        if 'drone_lat' in app.stats:
            print("✅ GPS statistics integrated")
        else:
            print("⚠️ GPS statistics not found")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        return False

def show_geotracking_demo():
    """Show a demo of geotracking features"""
    demo_info = """
🌍 GEOTRACKING FEATURES NOW AVAILABLE!

✅ Real-time GPS Tracking:
   • Drone position monitoring with lat/lon/altitude
   • GPS coordinates displayed in video header
   • Flight path recording with timestamps

✅ People Geo-positioning:
   • Convert pixel coordinates to GPS locations
   • Track people movement with GPS accuracy
   • Export geo-tagged people data

✅ Interactive Mapping:
   • Web-based maps with flight paths
   • People positions marked on map
   • Geofence monitoring and alerts

✅ Enhanced Statistics:
   • Live GPS coordinates in stats panel
   • Flight distance and altitude tracking
   • People geo-tracking count

✅ GPS Controls:
   • Enable/disable GPS tracking checkbox
   • Interactive map button (🗺️ Map)
   • GPS data export button (💾 Export)

🚀 HOW TO USE:
1. Launch: python run_tracking_gui.py
2. Check "🌍 GPS Tracking" in settings
3. Start tracking to see live GPS data
4. Click "🗺️ Map" to view interactive map
5. Click "💾 Export" to save GPS data

📍 GPS coordinates appear in:
   • Video header: "🌍 GPS: lat, lon | 🚁 altitude"
   • Statistics panel: Lat, Lon, Alt, Distance, Geo count
   • Interactive web map with flight path

The system uses GPS simulation by default for demonstration.
For real drone GPS, integrate with your drone's GPS system.
    """
    
    messagebox.showinfo("🌍 Geotracking Features Available!", demo_info)

def main():
    """Main test function"""
    print("🧪 Testing Enhanced Person Tracking with Geotracking")
    print("=" * 60)
    
    # Test core geotracking
    geotracking_ok = test_geotracking_features()
    
    # Test GUI integration
    gui_integration_ok = test_gui_integration()
    
    # Show results
    print("\n📊 TEST RESULTS")
    print("=" * 30)
    
    if geotracking_ok:
        print("✅ Geotracking system: WORKING")
    else:
        print("❌ Geotracking system: FAILED")
    
    if gui_integration_ok:
        print("✅ GUI integration: WORKING")
    else:
        print("❌ GUI integration: FAILED")
    
    if geotracking_ok and gui_integration_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("🌍 Geotracking features are fully integrated and working!")
        
        # Show demo info
        root = tk.Tk()
        root.withdraw()
        show_geotracking_demo()
        root.destroy()
        
    else:
        print("\n⚠️ Some tests failed. Check error messages above.")
        
        if not geotracking_ok:
            print("💡 Install geotracking dependencies: pip install folium geopy")

if __name__ == "__main__":
    main()
