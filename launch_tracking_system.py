#!/usr/bin/env python3
"""
Enhanced Person Tracking System Launcher
Choose between different GUI versions and features
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

class TrackingSystemLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("🎯 Enhanced Person Tracking System Launcher")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a1a')
        
        self.setup_gui()
        self.check_dependencies()
    
    def setup_gui(self):
        """Setup the launcher GUI"""
        # Title
        title_frame = tk.Frame(self.root, bg='#2d2d2d', height=80)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame, text="🎯 Enhanced Person Tracking System", 
                bg='#2d2d2d', fg='white', font=('Segoe UI', 18, 'bold')).pack(pady=20)
        
        # Main content
        content_frame = tk.Frame(self.root, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Description
        desc_text = """
🌟 Professional AI-Powered Person Tracking with Multiple Interface Options

Choose your preferred interface based on your needs:
        """
        
        tk.Label(content_frame, text=desc_text, bg='#1a1a1a', fg='#cccccc', 
                font=('Segoe UI', 11), justify=tk.LEFT).pack(pady=(0, 20))
        
        # GUI Options
        options_frame = tk.Frame(content_frame, bg='#1a1a1a')
        options_frame.pack(fill=tk.BOTH, expand=True)
        
        # Standard GUI
        self.create_option_card(options_frame, 
            "🎮 Standard Tracking GUI",
            "Complete person tracking with bird's-eye view and grid map",
            "• Real-time video processing\n• Bird's-eye view mapping\n• Live grid tactical view\n• Comprehensive statistics",
            self.launch_standard_gui,
            "#00d4aa"
        )
        
        # Geotracking GUI
        self.create_option_card(options_frame,
            "🌍 Geotracking GUI",
            "Advanced tracking with GPS coordinates and mapping",
            "• GPS coordinate tracking\n• Interactive web mapping\n• Geospatial analytics\n• Flight path recording",
            self.launch_geo_gui,
            "#4ecdc4"
        )
        
        # Command Line
        self.create_option_card(options_frame,
            "💻 Command Line Interface",
            "Direct command-line processing for automation",
            "• Batch processing\n• Scriptable interface\n• High performance\n• Automation friendly",
            self.launch_command_line,
            "#96ceb4"
        )
        
        # Demo System
        self.create_option_card(options_frame,
            "🎪 Demo & Testing",
            "Interactive demos and system testing",
            "• Grid map demonstration\n• Feature showcases\n• System testing\n• Example scenarios",
            self.launch_demos,
            "#feca57"
        )
        
        # Status bar
        self.status_frame = tk.Frame(self.root, bg='#2d2d2d', height=40)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(self.status_frame, text="Ready to launch tracking system", 
                                   bg='#2d2d2d', fg='white', font=('Segoe UI', 10))
        self.status_label.pack(pady=10)
    
    def create_option_card(self, parent, title, subtitle, features, command, color):
        """Create an option card for each GUI type"""
        card_frame = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=2)
        card_frame.pack(fill=tk.X, pady=5)
        
        # Header
        header_frame = tk.Frame(card_frame, bg=color, height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text=title, bg=color, fg='black', 
                font=('Segoe UI', 14, 'bold')).pack(side=tk.LEFT, padx=15, pady=12)
        
        tk.Button(header_frame, text="Launch", command=command, 
                 bg='white', fg='black', font=('Segoe UI', 10, 'bold'),
                 padx=20, pady=5).pack(side=tk.RIGHT, padx=15, pady=10)
        
        # Content
        content_frame = tk.Frame(card_frame, bg='#2d2d2d')
        content_frame.pack(fill=tk.X, padx=15, pady=10)
        
        tk.Label(content_frame, text=subtitle, bg='#2d2d2d', fg='white', 
                font=('Segoe UI', 11)).pack(anchor=tk.W)
        
        tk.Label(content_frame, text=features, bg='#2d2d2d', fg='#cccccc', 
                font=('Segoe UI', 9), justify=tk.LEFT).pack(anchor=tk.W, pady=(5, 0))
    
    def check_dependencies(self):
        """Check if all dependencies are available"""
        try:
            import cv2
            import numpy as np
            from ultralytics import YOLO
            self.update_status("✅ Core dependencies available")
            
            # Check optional dependencies
            try:
                import folium
                import geopy
                self.update_status("✅ All dependencies including geotracking available")
            except ImportError:
                self.update_status("⚠️ Geotracking dependencies missing (folium, geopy)")
                
        except ImportError as e:
            self.update_status(f"❌ Missing dependencies: {e}")
            messagebox.showerror("Dependencies Missing", 
                               f"Required dependencies not found: {e}\n\n"
                               "Please install with:\n"
                               "pip install opencv-python ultralytics numpy pillow tkinter")
    
    def launch_standard_gui(self):
        """Launch the standard tracking GUI"""
        try:
            self.update_status("🚀 Launching Standard Tracking GUI...")
            subprocess.Popen([sys.executable, "run_tracking_gui.py"])
            self.update_status("✅ Standard GUI launched successfully")
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch standard GUI: {e}")
            self.update_status(f"❌ Launch failed: {e}")
    
    def launch_geo_gui(self):
        """Launch the geotracking GUI"""
        try:
            # Check if geotracking dependencies are available
            import folium
            import geopy
            
            self.update_status("🌍 Launching Geotracking GUI...")
            subprocess.Popen([sys.executable, "geo_tracking_gui.py"])
            self.update_status("✅ Geotracking GUI launched successfully")
        except ImportError:
            messagebox.showerror("Dependencies Missing", 
                               "Geotracking requires additional dependencies:\n\n"
                               "pip install folium geopy\n\n"
                               "Install these packages and try again.")
            self.update_status("❌ Geotracking dependencies missing")
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch geotracking GUI: {e}")
            self.update_status(f"❌ Launch failed: {e}")
    
    def launch_command_line(self):
        """Show command line options"""
        cmd_info = """
💻 Command Line Interface Options:

Basic Usage:
python obj_counter.py --input video.mp4

Advanced Options:
python obj_counter.py --input 0 --confidence 0.7 --show-bird-eye --show-perspective

Geotracking:
python obj_counter.py --input video.mp4 --use-geotracking --export-geo-data

High Accuracy Mode:
python obj_counter.py --input video.mp4 --confidence 0.8 --min-track-duration 25

Analysis Mode (no display):
python obj_counter.py --input video.mp4 --no-display --confidence 0.7

For full options: python obj_counter.py --help
        """
        
        messagebox.showinfo("Command Line Interface", cmd_info)
        self.update_status("💻 Command line options displayed")
    
    def launch_demos(self):
        """Launch demo system"""
        demo_options = """
🎪 Available Demos:

1. Interactive Demo Launcher
2. Grid Map Demonstration  
3. System Testing
4. Feature Showcase

Which demo would you like to run?
        """
        
        result = messagebox.askyesnocancel("Demo Options", 
                                         demo_options + "\n\nYes: Demo Launcher\nNo: Grid Map Demo\nCancel: System Test")
        
        try:
            if result is True:
                subprocess.Popen([sys.executable, "demo_gui.py"])
                self.update_status("🎪 Demo launcher started")
            elif result is False:
                subprocess.Popen([sys.executable, "demo_grid_map.py"])
                self.update_status("🗺️ Grid map demo started")
            elif result is None:
                subprocess.Popen([sys.executable, "test_gui.py"])
                self.update_status("🧪 System test started")
        except Exception as e:
            messagebox.showerror("Demo Error", f"Failed to launch demo: {e}")
            self.update_status(f"❌ Demo launch failed: {e}")
    
    def update_status(self, message):
        """Update status bar"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

def main():
    """Main launcher entry point"""
    root = tk.Tk()
    app = TrackingSystemLauncher(root)
    root.mainloop()

if __name__ == "__main__":
    main()
